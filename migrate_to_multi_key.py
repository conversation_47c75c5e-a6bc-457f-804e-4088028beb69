#!/usr/bin/env python3
"""
Migration script to convert single API key configuration to multi-key format.
This script helps users upgrade their policy_config.json to support multiple API keys.
"""

import json
import os
import sys
from typing import List

def load_config(config_path: str) -> dict:
    """Load existing configuration"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ Configuration file not found: {config_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in configuration file: {e}")
        sys.exit(1)

def backup_config(config_path: str) -> str:
    """Create backup of existing configuration"""
    backup_path = f"{config_path}.backup"
    try:
        with open(config_path, 'r') as src, open(backup_path, 'w') as dst:
            dst.write(src.read())
        print(f"✅ Backup created: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"❌ Failed to create backup: {e}")
        sys.exit(1)

def migrate_config(config: dict, additional_keys: List[str]) -> dict:
    """Migrate configuration to multi-key format"""
    ai_config = config.get("ai", {})
    
    # Get existing key(s)
    existing_keys = []
    
    if "gemini_api_keys" in ai_config:
        # Already in multi-key format
        existing_keys = ai_config["gemini_api_keys"]
        print("✅ Configuration already uses multi-key format")
    elif "gemini_api_key" in ai_config:
        # Single key format - migrate
        existing_keys = [ai_config["gemini_api_key"]]
        print("🔄 Migrating from single key to multi-key format")
        # Remove old single key field
        del ai_config["gemini_api_key"]
    else:
        print("❌ No API key found in configuration")
        sys.exit(1)
    
    # Combine existing and new keys
    all_keys = existing_keys + additional_keys
    
    # Remove duplicates while preserving order
    unique_keys = []
    for key in all_keys:
        if key not in unique_keys:
            unique_keys.append(key)
    
    # Update configuration
    ai_config["gemini_api_keys"] = unique_keys
    config["ai"] = ai_config
    
    print(f"✅ Configuration updated with {len(unique_keys)} API keys")
    return config

def save_config(config: dict, config_path: str):
    """Save updated configuration"""
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        print(f"✅ Configuration saved: {config_path}")
    except Exception as e:
        print(f"❌ Failed to save configuration: {e}")
        sys.exit(1)

def validate_api_key(key: str) -> bool:
    """Basic validation of API key format"""
    return len(key) >= 20 and key.startswith("AIza")

def main():
    print("🚀 Multi-API Key Migration Tool")
    print("=" * 40)
    
    # Configuration file path
    config_path = "/opt/app/PolicyDW/policy_config.json"
    if len(sys.argv) > 1:
        config_path = sys.argv[1]
    
    print(f"📁 Configuration file: {config_path}")
    
    # Load existing configuration
    config = load_config(config_path)
    
    # Create backup
    backup_config(config_path)
    
    # Get additional API keys from user
    print("\n🔑 Enter additional API keys (press Enter with empty line to finish):")
    additional_keys = []
    
    while True:
        key = input(f"API Key #{len(additional_keys) + 1} (or press Enter to finish): ").strip()
        if not key:
            break
        
        if not validate_api_key(key):
            print("⚠️  Warning: API key format looks unusual. Continue anyway? (y/n): ", end="")
            if input().lower() != 'y':
                continue
        
        additional_keys.append(key)
        print(f"✅ Added API key #{len(additional_keys)}")
    
    if not additional_keys:
        print("ℹ️  No additional keys provided. Configuration unchanged.")
        return
    
    # Migrate configuration
    updated_config = migrate_config(config, additional_keys)
    
    # Show summary
    print(f"\n📊 Summary:")
    print(f"   Total API keys: {len(updated_config['ai']['gemini_api_keys'])}")
    print(f"   Expected daily capacity: {len(updated_config['ai']['gemini_api_keys']) * 1500} requests")
    print(f"   Estimated processing capacity: {len(updated_config['ai']['gemini_api_keys']) * 5000}-{len(updated_config['ai']['gemini_api_keys']) * 15000} policies/day")
    
    # Confirm save
    print(f"\n💾 Save updated configuration? (y/n): ", end="")
    if input().lower() == 'y':
        save_config(updated_config, config_path)
        print("\n🎉 Migration completed successfully!")
        print("\nNext steps:")
        print("1. Test the configuration with a small batch")
        print("2. Monitor logs for successful key rotation")
        print("3. Gradually increase batch size as needed")
    else:
        print("❌ Migration cancelled. Original configuration unchanged.")

if __name__ == "__main__":
    main()
