#!/usr/bin/env python3
"""
Script to check the processing status of policy_dimension_grader.py
"""

import json
import psycopg2
from datetime import datetime

# Load configuration
CONFIG_FILE = "/opt/app/PolicyDW/policy_config.json"

def load_config():
    with open(CONFIG_FILE, "r") as f:
        return json.load(f)

def get_processing_status():
    """Get comprehensive processing status"""
    config = load_config()
    db_config = config["database"]
    
    try:
        conn = psycopg2.connect(
            dbname=db_config["dbname"],
            user=db_config["user"],
            password=db_config["password"],
            host=db_config["host"],
            port=db_config["port"]
        )
        
        with conn.cursor() as cur:
            # Get total policies
            cur.execute("SELECT COUNT(*) FROM policy_data")
            total_policies = cur.fetchone()[0]
            
            # Get processed policies (with all dimensions filled)
            cur.execute("""
                SELECT COUNT(*) 
                FROM policy_data_processed 
                WHERE target_clarity IS NOT NULL 
                  AND legal_binding IS NOT NULL 
                  AND financial_support IS NOT NULL 
                  AND implementation_mechanism IS NOT NULL 
                  AND duration IS NOT NULL 
                  AND supporting_policies IS NOT NULL
            """)
            fully_processed = cur.fetchone()[0]
            
            # Get unprocessed policies (matching the script's criteria)
            cur.execute("""
                SELECT COUNT(*)
                FROM policy_data a
                JOIN policy_data_processed b ON a.id = b.id
                WHERE (b.target_clarity IS NULL OR
                       b.legal_binding IS NULL OR
                       b.financial_support IS NULL OR
                       b.implementation_mechanism IS NULL OR
                       b.duration IS NULL OR
                       b.supporting_policies IS NULL)
                  AND a.policy_title IS NOT NULL
                  AND a.full_article IS NOT NULL
                  AND LENGTH(TRIM(a.full_article)) > 50
            """)
            unprocessed = cur.fetchone()[0]
            
            # Get recent processing activity (last 24 hours)
            cur.execute("""
                SELECT COUNT(*) 
                FROM policy_data_processed 
                WHERE processed_at >= CURRENT_TIMESTAMP - INTERVAL '24 hours'
            """)
            recent_processed = cur.fetchone()[0]
            
        conn.close()
        
        # Calculate completion percentage
        completion_pct = (fully_processed / total_policies * 100) if total_policies > 0 else 0
        
        return {
            'total_policies': total_policies,
            'fully_processed': fully_processed,
            'unprocessed': unprocessed,
            'recent_processed_24h': recent_processed,
            'completion_percentage': completion_pct
        }
        
    except Exception as e:
        print(f"Error connecting to database: {e}")
        return None

def print_status_report():
    """Print a formatted status report"""
    print("=" * 60)
    print("POLICY DIMENSION GRADER - PROCESSING STATUS")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    status = get_processing_status()
    if not status:
        print("❌ Unable to retrieve status information")
        return
    
    print(f"📊 OVERALL PROGRESS:")
    print(f"   Total Policies:        {status['total_policies']:,}")
    print(f"   Fully Processed:       {status['fully_processed']:,}")
    print(f"   Remaining:             {status['unprocessed']:,}")
    print(f"   Completion:            {status['completion_percentage']:.1f}%")
    print()
    
    print(f"⏱️  RECENT ACTIVITY:")
    print(f"   Processed (24h):       {status['recent_processed_24h']:,}")
    print()
    
    # Progress bar
    bar_length = 40
    filled_length = int(bar_length * status['completion_percentage'] / 100)
    bar = '█' * filled_length + '░' * (bar_length - filled_length)
    print(f"📈 PROGRESS: [{bar}] {status['completion_percentage']:.1f}%")
    print()
    
    if status['unprocessed'] > 0:
        print(f"🔄 Status: Processing in progress...")
        print(f"   Estimated remaining: {status['unprocessed']:,} policies")
    else:
        print("✅ Status: All policies have been processed!")
    
    print("=" * 60)

if __name__ == "__main__":
    print_status_report()
