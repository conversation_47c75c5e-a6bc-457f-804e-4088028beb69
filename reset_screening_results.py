#!/usr/bin/env python3
"""
重置政策筛选结果
清除raw_processed_policies表中的should_be_deleted和delete_reason字段
"""

import json
import psycopg2
import logging
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 加载配置
CONFIG_FILE = "policy_config.json"

try:
    with open(CONFIG_FILE, "r") as f:
        config = json.load(f)
    
    DB_CONFIG = config["database"]
    logger.info("✅ Configuration loaded successfully")
except Exception as e:
    logger.error(f"❌ Error loading configuration: {e}")
    sys.exit(1)

def connect_database():
    """连接PostgreSQL数据库"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        logger.info("✅ Database connection established")
        return conn
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return None

def get_screening_stats(conn):
    """获取当前筛选统计信息"""
    try:
        query = """
        SELECT 
            COUNT(*) as total_policies,
            SUM(CASE WHEN should_be_deleted IS NOT NULL THEN 1 ELSE 0 END) as processed_policies,
            SUM(CASE WHEN should_be_deleted = true THEN 1 ELSE 0 END) as to_delete,
            SUM(CASE WHEN should_be_deleted = false THEN 1 ELSE 0 END) as to_keep
        FROM raw_processed_policies 
        WHERE city IS NOT NULL AND city <> ''
        """
        
        with conn.cursor() as cur:
            cur.execute(query)
            result = cur.fetchone()
            
        logger.info(f"📊 Current screening statistics:")
        logger.info(f"  Total policies: {result[0]}")
        logger.info(f"  Processed policies: {result[1]}")
        logger.info(f"  Marked for deletion: {result[2]}")
        logger.info(f"  Marked to keep: {result[3]}")
        
        return result
    except Exception as e:
        logger.error(f"❌ Failed to get statistics: {e}")
        return None

def reset_screening_results(conn):
    """重置所有筛选结果"""
    try:
        query = """
        UPDATE raw_processed_policies 
        SET should_be_deleted = NULL, delete_reason = NULL
        WHERE should_be_deleted IS NOT NULL OR delete_reason IS NOT NULL
        """
        
        with conn.cursor() as cur:
            cur.execute(query)
            affected_rows = cur.rowcount
            conn.commit()
        
        logger.info(f"✅ Reset {affected_rows} records")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to reset screening results: {e}")
        conn.rollback()
        return False

def main():
    """主函数"""
    logger.info("🔄 Starting screening results reset")
    
    # 连接数据库
    conn = connect_database()
    if not conn:
        return
    
    try:
        # 获取当前统计信息
        stats = get_screening_stats(conn)
        if not stats:
            return
        
        if stats[1] == 0:  # 没有已处理的记录
            logger.info("ℹ️ No processed records found, nothing to reset")
            return
        
        # 确认重置
        logger.info(f"⚠️ This will reset {stats[1]} processed records")
        user_input = input("Are you sure you want to reset all screening results? (yes/no): ").strip().lower()
        
        if user_input != 'yes':
            logger.info("❌ Reset cancelled by user")
            return
        
        # 执行重置
        if reset_screening_results(conn):
            logger.info("🎉 Screening results reset successfully")
            
            # 验证重置结果
            new_stats = get_screening_stats(conn)
            if new_stats and new_stats[1] == 0:
                logger.info("✅ Verification passed - all records reset")
            else:
                logger.warning("⚠️ Verification failed - some records may not have been reset")
        else:
            logger.error("❌ Failed to reset screening results")
    
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
    finally:
        conn.close()
        logger.info("🔚 Database connection closed")

if __name__ == "__main__":
    main()
