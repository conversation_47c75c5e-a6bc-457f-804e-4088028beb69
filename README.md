# Policy Data Warehouse

## Description

This project implements a data warehouse for policy data, focusing on Chinese policies across various sectors and cities. The data warehouse is designed to facilitate analysis and reporting on policy trends and their impact.

## Data Model

The data warehouse follows a star schema with the following dimension and fact tables:

*   **City_Dimension:** Stores information about Chinese cities, including their province and region.
*   **Sector_Dimension:** Stores information about policy sectors, organized in a hierarchical structure.
*   **Policy_Level_Dimension:** Stores information about policy levels (e.g., national, provincial, city).
*   **Time_Dimension:** Stores information about time, including year, month, and quarter.
*   **Policy_Facts:** The fact table that links the dimensions together and stores key metrics about policies.
*   **Policy_Details:** Stores detailed information about each policy, such as the policy title, issuing department, and full article text.
*   **Policy_Relationships:** Stores relationships between policies, such as parent-child or related policies.

## Files

*   `etl_sector_dimension.py`: This script extracts sector dimension data from a hierarchical Python dictionary, transforms it into a Pandas DataFrame, and loads it into the `Sector_Dimension` table in the PostgreSQL database.
*   `dw/init.sql`: This SQL script creates the database schema for the policy data warehouse, including all the dimension and fact tables.
*   `dw/China_City_List.csv`: This CSV file contains a list of Chinese cities, their provinces, and regions, used to populate the `City_Dimension` table.
*   `README.md`: This file provides a description of the project and instructions on how to use it.

## Usage

1.  **Create the database schema:** Run the `dw/init.sql` script in your PostgreSQL database.
2.  **Populate the City_Dimension table:** Import the `dw/China_City_List.csv` file into the `City_Dimension` table.
3.  **Populate the Sector_Dimension table:** Run the `etl_sector_dimension.py` script to populate the `Sector_Dimension` table.
4.  **Populate the remaining dimension tables:** Create and populate the `Policy_Level_Dimension` and `Time_Dimension` tables with relevant data.
5.  **Populate the Policy_Facts, Policy_Details, and Policy_Relationships tables:** Extract policy data from various sources, transform it into the appropriate format, and load it into these tables.

## Configuration

The `etl_sector_dimension.py` script uses the following database configuration:

```
db_config = {
    "host": "j-5181725-job-0",
    "port": "5432",
    "database": "mydb",
    "user": "ucloud",
    "password": "Abcd1234"
}
```

Make sure to update these values to match your PostgreSQL database configuration.

## License

[Insert License Information Here]
