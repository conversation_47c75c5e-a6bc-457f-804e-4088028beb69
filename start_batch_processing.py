#!/usr/bin/env python3
"""
启动批量政策筛选处理
提供不同的处理选项和监控功能
"""

import os
import sys
import json
import time
import logging
import psycopg2
import argparse
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 加载配置
CONFIG_FILE = "policy_config.json"

try:
    with open(CONFIG_FILE, "r") as f:
        config = json.load(f)
    
    DB_CONFIG = config["database"]
    logger.info("✅ Configuration loaded successfully")
except Exception as e:
    logger.error(f"❌ Error loading configuration: {e}")
    sys.exit(1)

def connect_database():
    """连接PostgreSQL数据库"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        logger.info("✅ Database connection established")
        return conn
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return None

def get_processing_stats(conn):
    """获取处理统计信息"""
    try:
        query = """
        SELECT 
            COUNT(DISTINCT city) as total_cities,
            COUNT(DISTINCT CASE WHEN should_be_deleted IS NOT NULL THEN city END) as processed_cities,
            COUNT(*) as total_policies,
            SUM(CASE WHEN should_be_deleted IS NOT NULL THEN 1 ELSE 0 END) as processed_policies,
            SUM(CASE WHEN should_be_deleted = true THEN 1 ELSE 0 END) as to_delete,
            SUM(CASE WHEN should_be_deleted = false THEN 1 ELSE 0 END) as to_keep
        FROM raw_processed_policies 
        WHERE city IS NOT NULL AND city <> ''
        """
        
        with conn.cursor() as cur:
            cur.execute(query)
            result = cur.fetchone()
        
        stats = {
            'total_cities': result[0],
            'processed_cities': result[1],
            'total_policies': result[2],
            'processed_policies': result[3],
            'to_delete': result[4],
            'to_keep': result[5]
        }
        
        return stats
    except Exception as e:
        logger.error(f"❌ Failed to get statistics: {e}")
        return None

def display_stats(stats):
    """显示统计信息"""
    if not stats:
        return
    
    logger.info("📊 Current Processing Statistics:")
    logger.info(f"  Cities: {stats['processed_cities']}/{stats['total_cities']} processed")
    logger.info(f"  Policies: {stats['processed_policies']}/{stats['total_policies']} processed")
    
    if stats['processed_policies'] > 0:
        delete_rate = (stats['to_delete'] / stats['processed_policies']) * 100
        keep_rate = (stats['to_keep'] / stats['processed_policies']) * 100
        logger.info(f"  Results: {stats['to_delete']} to delete ({delete_rate:.1f}%), {stats['to_keep']} to keep ({keep_rate:.1f}%)")
    
    if stats['total_policies'] > 0:
        progress = (stats['processed_policies'] / stats['total_policies']) * 100
        logger.info(f"  Overall Progress: {progress:.2f}%")

def get_unprocessed_cities(conn, limit=None):
    """获取未处理的城市列表"""
    try:
        query = """
        SELECT city, COUNT(*) as policy_count,
               SUM(CASE WHEN should_be_deleted IS NOT NULL THEN 1 ELSE 0 END) as processed_count
        FROM raw_processed_policies
        WHERE city IS NOT NULL AND city <> ''
        GROUP BY city
        HAVING SUM(CASE WHEN should_be_deleted IS NOT NULL THEN 1 ELSE 0 END) = 0
        ORDER BY policy_count ASC
        """
        
        if limit:
            query += f" LIMIT {limit}"
        
        with conn.cursor() as cur:
            cur.execute(query)
            cities = cur.fetchall()
        
        return cities
    except Exception as e:
        logger.error(f"❌ Failed to get unprocessed cities: {e}")
        return []

def start_processing(batch_size=10, start_from=0):
    """启动批量处理"""
    logger.info(f"🚀 Starting batch processing with batch_size={batch_size}, start_from={start_from}")
    
    cmd = f"python batch_city_screening.py --start {start_from} --limit {batch_size}"
    logger.info(f"📝 Executing: {cmd}")
    
    # 记录开始时间
    start_time = datetime.now()
    logger.info(f"⏰ Processing started at: {start_time}")
    
    # 执行命令
    exit_code = os.system(cmd)
    
    # 记录结束时间
    end_time = datetime.now()
    duration = end_time - start_time
    logger.info(f"⏰ Processing completed at: {end_time}")
    logger.info(f"⏱️ Duration: {duration}")
    
    if exit_code == 0:
        logger.info("✅ Batch processing completed successfully")
    else:
        logger.error(f"❌ Batch processing failed with exit code: {exit_code}")
    
    return exit_code == 0

def monitor_progress():
    """监控处理进度"""
    conn = connect_database()
    if not conn:
        return
    
    try:
        while True:
            stats = get_processing_stats(conn)
            display_stats(stats)
            
            if stats and stats['processed_cities'] == stats['total_cities']:
                logger.info("🎉 All cities have been processed!")
                break
            
            logger.info("⏳ Waiting 30 seconds before next check...")
            time.sleep(30)
    
    except KeyboardInterrupt:
        logger.info("⏹️ Monitoring stopped by user")
    finally:
        conn.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动批量政策筛选处理")
    parser.add_argument("--stats", action="store_true", help="显示当前统计信息")
    parser.add_argument("--monitor", action="store_true", help="监控处理进度")
    parser.add_argument("--start", type=int, default=0, help="起始城市索引")
    parser.add_argument("--batch-size", type=int, default=10, help="批次大小")
    parser.add_argument("--list-unprocessed", type=int, help="列出未处理的城市（指定数量）")
    parser.add_argument("--process", action="store_true", help="开始批量处理")
    
    args = parser.parse_args()
    
    conn = connect_database()
    if not conn:
        return
    
    try:
        if args.stats:
            stats = get_processing_stats(conn)
            display_stats(stats)
        
        elif args.monitor:
            monitor_progress()
        
        elif args.list_unprocessed:
            cities = get_unprocessed_cities(conn, args.list_unprocessed)
            logger.info(f"📋 Next {len(cities)} unprocessed cities:")
            for i, (city, policy_count, processed_count) in enumerate(cities, 1):
                logger.info(f"  {i}. {city}: {policy_count} policies")
        
        elif args.process:
            start_processing(args.batch_size, args.start)
        
        else:
            # 默认显示统计信息和下一批城市
            stats = get_processing_stats(conn)
            display_stats(stats)
            
            if stats and stats['processed_cities'] < stats['total_cities']:
                logger.info("\n📋 Next 5 cities to process:")
                cities = get_unprocessed_cities(conn, 5)
                for i, (city, policy_count, processed_count) in enumerate(cities, 1):
                    logger.info(f"  {i}. {city}: {policy_count} policies")
                
                logger.info("\n💡 Usage examples:")
                logger.info("  python start_batch_processing.py --process --batch-size 5")
                logger.info("  python start_batch_processing.py --monitor")
                logger.info("  python start_batch_processing.py --list-unprocessed 10")
    
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
