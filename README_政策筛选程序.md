# 政策数据Gemini筛选程序使用说明

## 概述

本程序套件用于对`raw_processed_policies`表中的政策数据进行智能筛选，使用Google Gemini API按城市分组处理政策数据，根据预设规则判断政策是否应该被删除。

## 程序文件说明

### 1. `policy_gemini_screening.py` - 主程序
完整的政策筛选程序，支持处理所有城市的政策数据。

**功能特点：**
- 自动获取所有需要处理的城市
- 支持API密钥轮换
- 支持断点续传
- 完整的错误处理和重试机制
- 详细的日志记录

### 2. `test_city_screening.py` - 测试程序
用于测试单个城市的政策筛选功能。

**功能特点：**
- 测试指定城市的政策筛选
- 限制处理数量（用于测试）
- 显示详细的AI响应
- 可选择是否保存结果到数据库

### 3. `batch_city_screening.py` - 批量处理程序
支持更灵活的批量处理选项。

**功能特点：**
- 支持指定起始位置和处理数量
- 支持城市名称模式匹配
- 支持干运行模式（仅显示将要处理的城市）
- 详细的处理统计

## 筛选规则

程序使用以下规则对政策进行筛选：

1. **地域相关性**：如果政策仅适用于目标城市以外的地区，标记为删除
2. **重复政策**：同一政策的不同版本，仅保留最权威的版本
3. **适用性**：只要政策适用于目标城市，则保留
4. **行业相关性**：仅针对目标城市没有的行业/区域的政策，标记为删除
5. **专业判断**：结合政策内容、部门和行政常识进行判断

## 使用方法

### 1. 测试单个城市

```bash
# 测试惠州市的前5条政策
python test_city_screening.py 惠州市 5

# 测试深圳市的前10条政策
python test_city_screening.py 深圳市 10
```

### 2. 批量处理城市

```bash
# 处理所有城市
python batch_city_screening.py

# 从第10个城市开始处理，限制处理20个城市
python batch_city_screening.py --start 10 --limit 20

# 只处理包含"深圳"的城市
python batch_city_screening.py --pattern 深圳

# 干运行模式，查看将要处理的城市
python batch_city_screening.py --dry-run --limit 10
```

### 3. 完整处理

```bash
# 运行主程序处理所有城市
python policy_gemini_screening.py
```

## 配置说明

程序使用`policy_config.json`配置文件，主要配置项：

```json
{
    "database": {
        "dbname": "mydb",
        "user": "xiuli", 
        "password": "73Lxf1017",
        "host": "localhost",
        "port": 5432
    },
    "ai": {
        "gemini_api_keys": ["key1", "key2", ...],
        "model": "gemini-2.0-flash-lite",
        "max_retries": 5,
        "retry_delay": 2,
        "rate_limit_per_minute": 30,
        "batch_size": 200
    }
}
```

## 数据库字段说明

程序会更新`raw_processed_policies`表的以下字段：

- `should_be_deleted`: 布尔值，true表示应该删除，false表示保留
- `delete_reason`: 文本，删除或保留的具体理由

## 输出格式

AI返回的每行结果格式：
```
policy_id,true/false,具体理由
```

示例：
```
116671,true,仅适用于东莞市，与惠州市无关
116467,true,同一政策仅保留最权威、最正式版本，其余为重复
116857,false,保留
```

## 日志文件

- `policy_gemini_screening.log` - 主程序日志
- `batch_city_screening.log` - 批量处理程序日志

## 注意事项

1. **API配额管理**：程序支持多个API密钥轮换，避免配额限制
2. **速率限制**：程序内置速率限制，避免触发API限制
3. **断点续传**：程序会跳过已处理的政策，支持中断后继续处理
4. **错误处理**：完整的错误处理和重试机制
5. **数据备份**：建议在运行前备份数据库

## 监控处理进度

可以通过以下SQL查询监控处理进度：

```sql
-- 查看各城市处理进度
SELECT 
    city,
    COUNT(*) as total_policies,
    SUM(CASE WHEN should_be_deleted IS NOT NULL THEN 1 ELSE 0 END) as processed_policies,
    SUM(CASE WHEN should_be_deleted = true THEN 1 ELSE 0 END) as to_delete,
    SUM(CASE WHEN should_be_deleted = false THEN 1 ELSE 0 END) as to_keep
FROM raw_processed_policies 
WHERE city IS NOT NULL AND city <> ''
GROUP BY city
ORDER BY processed_policies DESC;

-- 查看总体进度
SELECT 
    COUNT(*) as total_policies,
    SUM(CASE WHEN should_be_deleted IS NOT NULL THEN 1 ELSE 0 END) as processed_policies,
    ROUND(100.0 * SUM(CASE WHEN should_be_deleted IS NOT NULL THEN 1 ELSE 0 END) / COUNT(*), 2) as progress_percent
FROM raw_processed_policies 
WHERE city IS NOT NULL AND city <> '';
```

## 故障排除

1. **API密钥问题**：检查配置文件中的API密钥是否有效
2. **数据库连接问题**：检查数据库配置和网络连接
3. **内存不足**：调整batch_size参数
4. **网络超时**：增加retry_delay和max_retries参数

## 性能优化建议

1. 根据API配额调整`rate_limit_per_minute`
2. 根据内存情况调整`batch_size`
3. 使用多个API密钥提高处理速度
4. 在网络稳定的环境下运行
