CREATE EXTENSION IF NOT EXISTS citext;

-- Drop existing tables if they exist
DROP TABLE IF EXISTS Policy_Relationships CASCADE;
DROP TABLE IF EXISTS Policy_Details CASCADE;
DROP TABLE IF EXISTS Policy_Facts CASCADE;
DROP TABLE IF EXISTS Time_Dimension CASCADE;
DROP TABLE IF EXISTS Policy_Level_Dimension CASCADE;
DROP TABLE IF EXISTS Sector_Dimension CASCADE;
DROP TABLE IF EXISTS City_Dimension CASCADE;

-- ----------------------------
--  Table structure for City_Dimension
-- ----------------------------
CREATE TABLE City_Dimension (
    City_ID SERIAL PRIMARY KEY,
    City_Name CITEXT NOT NULL,
    Province_Name VARCHAR(100),
    Region VARCHAR(100),
    Created_At TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    Updated_At TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

--  Index for City_Name for faster lookups
CREATE INDEX idx_city_name ON City_Dimension (City_Name);

-- ----------------------------
--  Table structure for Sector_Dimension
--  Self-referencing foreign key for hierarchical sectors
-- ----------------------------
CREATE TABLE Sector_Dimension (
    Sector_ID SERIAL PRIMARY KEY,
    Sector_Name CITEXT NOT NULL,
    Parent_Sector_ID INT REFERENCES Sector_Dimension(Sector_ID), -- Self-reference to define sector hierarchy
    Sector_Description TEXT,
    Created_At TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    Updated_At TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

--  Indexes for Sector_Name and Parent_Sector_ID
CREATE INDEX idx_sector_name ON Sector_Dimension (Sector_Name);
CREATE INDEX idx_parent_sector_id ON Sector_Dimension (Parent_Sector_ID);

-- ----------------------------
--  Table structure for Policy_Level_Dimension
-- ----------------------------
CREATE TABLE Policy_Level_Dimension (
    Policy_Level_ID SERIAL PRIMARY KEY,
    Policy_Level_Name CITEXT NOT NULL,
    Policy_Level_Description TEXT,
    Created_At TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    Updated_At TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

--  Index for Policy_Level_Name
CREATE INDEX idx_policy_level_name ON Policy_Level_Dimension (Policy_Level_Name);

-- ----------------------------
--  Table structure for Time_Dimension
-- ----------------------------
CREATE TABLE Time_Dimension (
    Time_ID SERIAL PRIMARY KEY,
    Year INT NOT NULL,
    Month INT, -- Optional: if you need monthly analysis
    Quarter INT, -- Optional: if you need quarterly analysis
    Created_At TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    Updated_At TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

--  Index for Year (most common time-based filter)
CREATE INDEX idx_time_year ON Time_Dimension (Year);

-- ----------------------------
--  Table structure for Policy_Facts (Fact Table)
--  Contains foreign keys to all dimension tables
-- ----------------------------
CREATE TABLE Policy_Facts (
    Policy_ID SERIAL PRIMARY KEY,
    City_ID INT NOT NULL REFERENCES City_Dimension(City_ID),
    Sector_ID INT NOT NULL REFERENCES Sector_Dimension(Sector_ID),
    Policy_Level_ID INT NOT NULL REFERENCES Policy_Level_Dimension(Policy_Level_ID),
    Time_ID INT NOT NULL REFERENCES Time_Dimension(Time_ID),
    Policy_Strength_Score NUMERIC, -- Optional: if you implement policy strength scoring
    Is_Valid_Policy BOOLEAN NOT NULL,
    Deletion_Reason VARCHAR(255), -- Optional: to store reason for deletion if policy is invalid
    Created_At TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    Updated_At TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

--  Indexes on Foreign Keys for join performance
CREATE INDEX idx_policy_facts_city_id ON Policy_Facts (City_ID);
CREATE INDEX idx_policy_facts_sector_id ON Policy_Facts (Sector_ID);
CREATE INDEX idx_policy_facts_policy_level_id ON Policy_Facts (Policy_Level_ID);
CREATE INDEX idx_policy_facts_time_id ON Policy_Facts (Time_ID);
CREATE INDEX idx_policy_valid_policy ON Policy_Facts (Is_Valid_Policy); -- Index for filtering valid policies

-- ----------------------------
--  Table structure for Policy_Details (Detail Table)
--  Stores detailed information about each policy, linked to Policy_Facts
-- ----------------------------
CREATE TABLE Policy_Details (
    Policy_ID INT PRIMARY KEY REFERENCES Policy_Facts(Policy_ID), -- Primary Key and Foreign Key
    Policy_Title TEXT,
    Issuing_Department VARCHAR(255),
    Announcement_Year VARCHAR(50),
    Starting_Year VARCHAR(50),
    Ending_Year VARCHAR(50), -- Optional: if available
    Web_Link TEXT,
    Full_Article_Text TEXT,
    Created_At TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    Updated_At TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for Policy Title for searching or filtering
CREATE INDEX idx_policy_details_title ON Policy_Details (Policy_Title);


-- ----------------------------
--  Table structure for Policy_Relationships (Relationship Table)
--  Models relationships between policies (e.g., parent-child, related policies)
-- ----------------------------
CREATE TABLE Policy_Relationships (
    Relationship_ID SERIAL PRIMARY KEY,
    Policy_1_ID INT NOT NULL REFERENCES Policy_Facts(Policy_ID),
    Policy_2_ID INT NOT NULL REFERENCES Policy_Facts(Policy_ID),
    Relationship_Type VARCHAR(100) NOT NULL, -- e.g., "上位政策 - 下位政策", "补充政策", "相关政策"
    Relationship_Description TEXT,
    Created_At TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    Updated_At TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (Policy_1_ID, Policy_2_ID, Relationship_Type) -- Composite unique index to prevent duplicate relationships
);

-- Indexes for Foreign Keys
CREATE INDEX idx_policy_relationships_policy_1_id ON Policy_Relationships (Policy_1_ID);
CREATE INDEX idx_policy_relationships_policy_2_id ON Policy_Relationships (Policy_2_ID);
CREATE INDEX idx_policy_relationships_type ON Policy_Relationships (Relationship_Type);


--  Optional: Add comments to tables and columns for better documentation (PostgreSQL specific syntax)
COMMENT ON TABLE City_Dimension IS '维度表: 城市信息';
COMMENT ON COLUMN City_Dimension.City_ID IS '城市ID，主键';
COMMENT ON COLUMN City_Dimension.City_Name IS '城市名称';
COMMENT ON COLUMN City_Dimension.Province_Name IS '省份名称';
COMMENT ON COLUMN City_Dimension.Region IS '地区 (可选)';

COMMENT ON TABLE Sector_Dimension IS '维度表: 行业部门信息';
COMMENT ON COLUMN Sector_Dimension.Sector_ID IS '行业部门ID，主键';
COMMENT ON COLUMN Sector_Dimension.Sector_Name IS '行业部门名称';
COMMENT ON COLUMN Sector_Dimension.Parent_Sector_ID IS '父级行业部门ID (自关联，可选，用于表示行业层级)';
COMMENT ON COLUMN Sector_Dimension.Sector_Description IS '行业部门描述 (可选)';

COMMENT ON TABLE Policy_Level_Dimension IS '维度表: 政策层级信息';
COMMENT ON COLUMN Policy_Level_Dimension.Policy_Level_ID IS '政策层级ID，主键';
COMMENT ON COLUMN Policy_Level_Dimension.Policy_Level_Name IS '政策层级名称 (e.g., 国家级, 省级, 市级)';
COMMENT ON COLUMN Policy_Level_Dimension.Policy_Level_Description IS '政策层级描述 (可选)';

COMMENT ON TABLE Time_Dimension IS '维度表: 时间维度信息';
COMMENT ON COLUMN Time_Dimension.Time_ID IS '时间维度ID，主键';
COMMENT ON COLUMN Time_Dimension.Year IS '年份';
COMMENT ON COLUMN Time_Dimension.Month IS '月份 (可选)';
COMMENT ON COLUMN Time_Dimension.Quarter IS '季度 (可选)';

COMMENT ON TABLE Policy_Facts IS '事实表: 政策核心信息';
COMMENT ON COLUMN Policy_Facts.Policy_ID IS '政策ID，主键';
COMMENT ON COLUMN Policy_Facts.City_ID IS '城市ID，外键关联到 City_Dimension';
COMMENT ON COLUMN Policy_Facts.Sector_ID IS '行业部门ID，外键关联到 Sector_Dimension';
COMMENT ON COLUMN Policy_Facts.Policy_Level_ID IS '政策层级ID，外键关联到 Policy_Level_Dimension';
COMMENT ON COLUMN Policy_Facts.Time_ID IS '时间维度ID，外键关联到 Time_Dimension';
COMMENT ON COLUMN Policy_Facts.Policy_Strength_Score IS '政策强度评分 (可选)';
COMMENT ON COLUMN Policy_Facts.Is_Valid_Policy IS '政策是否有效 (清洗结果)';
COMMENT ON COLUMN Policy_Facts.Deletion_Reason IS '政策删除原因 (可选)';

COMMENT ON TABLE Policy_Details IS '详情表: 政策详细内容';
COMMENT ON COLUMN Policy_Details.Policy_ID IS '政策ID，主键，外键关联到 Policy_Facts';
COMMENT ON COLUMN Policy_Details.Policy_Title IS '政策标题';
COMMENT ON COLUMN Policy_Details.Issuing_Department IS '发布部门';
COMMENT ON COLUMN Policy_Details.Announcement_Year IS '公告年份 (原始数据)';
COMMENT ON COLUMN Policy_Details.Starting_Year IS '起始年份 (原始数据)';
COMMENT ON COLUMN Policy_Details.Ending_Year IS '终止年份 (原始数据，可选)';
COMMENT ON COLUMN Policy_Details.Web_Link IS '政策原文链接';
COMMENT ON COLUMN Policy_Details.Full_Article_Text IS '政策全文内容';

COMMENT ON TABLE Policy_Relationships IS '关系表: 政策关系';
COMMENT ON COLUMN Policy_Relationships.Relationship_ID IS '关系ID，主键';
COMMENT ON COLUMN Policy_Relationships.Policy_1_ID IS '政策1 ID，外键关联到 Policy_Facts';
COMMENT ON COLUMN Policy_Relationships.Policy_2_ID IS '政策2 ID，外键关联到 Policy_Facts';
COMMENT ON COLUMN Policy_Relationships.Relationship_Type IS '关系类型 (e.g., 上位政策-下位政策)';
COMMENT ON COLUMN Policy_Relationships.Relationship_Description IS '关系描述 (可选)';
