import os
import json
import time
import pandas as pd
import psycopg2
import google.generativeai as genai
from datetime import datetime
import sys
import random
import re

# Load configuration from policy_config.json
CONFIG_FILE = "/opt/app/PolicyDW/policy_config.json"

with open(CONFIG_FILE, "r") as f:
    config = json.load(f)

# PostgreSQL Connection Details
DB_CONFIG = config["database"]

# AI Configuration
AI_CONFIG = config.get("ai", {})
GEMINI_API_KEY = AI_CONFIG.get("gemini_api_key", "")

# Try to get API key from environment variable if not in config or if empty
if not GEMINI_API_KEY:
    GEMINI_API_KEY = os.environ.get("GOOGLE_API_KEY", "")

# Verify we have an API key
if not GEMINI_API_KEY:
    print("⚠️ Warning: No Gemini API key found in config file or environment variables.")
    print("Please set the API key in policy_config.json or as GOO<PERSON>LE_API_KEY environment variable.")
    print("Example: export GOOGLE_API_KEY='your-api-key-here'")
    sys.exit(1)

# Debug: Show first few characters of API key to confirm it's loaded
print(f"API Key loaded (starts with: {GEMINI_API_KEY[:5]}...)")

AI_MODEL = AI_CONFIG.get("model", "gemini-2.0-flash-lite")
MAX_RETRIES = AI_CONFIG.get("max_retries", 5)
RETRY_DELAY = AI_CONFIG.get("retry_delay", 2)

# Get batch size from config (with default if not present)
BATCH_SIZE = config.get("batch_size", 10)
PROCESSED_TABLE = "policy_data_processed"

# Configure Gemini API Key
try:
    genai.configure(api_key=GEMINI_API_KEY)
    print("Gemini API configured successfully")
    
    # Verify configuration by initializing model
    model = genai.GenerativeModel(AI_MODEL)
    print(f"Gemini model '{AI_MODEL}' initialized")
except Exception as e:
    print(f"❌ Error configuring Gemini API: {e}")
    print("Please check your API key and internet connection")
    sys.exit(1)

# Create a connection to the PostgreSQL database
def connect_db():
    return psycopg2.connect(
        dbname=DB_CONFIG["dbname"],
        user=DB_CONFIG["user"],
        password=DB_CONFIG["password"],
        host=DB_CONFIG["host"],
        port=DB_CONFIG["port"]
    )

# Create the processed data table if it doesn't exist
def create_processed_table():
    conn = connect_db()
    cur = conn.cursor()
    try:
        # Check if table exists
        cur.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = %s
            )
        """, (PROCESSED_TABLE,))
        table_exists = cur.fetchone()[0]
        
        if not table_exists:
            print(f"Creating {PROCESSED_TABLE} table...")
            cur.execute(f"""
                CREATE TABLE {PROCESSED_TABLE} (
                    id INTEGER PRIMARY KEY,
                    policy_title TEXT,
                    is_valid_policy BOOLEAN,
                    is_low_carbon BOOLEAN,
                    policy_level TEXT,
                    is_related_to_city BOOLEAN,
                    target_sectors TEXT,
                    policy_strength INTEGER,
                    policy_strength_justification TEXT,
                    issuing_department TEXT,
                    announcement_date DATE,
                    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            conn.commit()
            print(f"Table {PROCESSED_TABLE} created successfully.")
        else:
            print(f"Table {PROCESSED_TABLE} already exists.")
    except Exception as e:
        conn.rollback()
        print(f"Error creating table: {e}")
    finally:
        cur.close()
        conn.close()

# Get list of cities to process
def get_cities():
    conn = connect_db()
    cur = conn.cursor()
    cities = []
    try:
        cur.execute("SELECT id, city FROM policy_cities ORDER BY id")
        cities = cur.fetchall()
        print(f"Found {len(cities)} cities to process")
    except Exception as e:
        print(f"Error fetching cities: {e}")
    finally:
        cur.close()
        conn.close()
    return cities

# Get policies for a specific city with pagination
def get_city_policies(city_id, offset, batch_size):
    conn = connect_db()
    cur = conn.cursor()
    policies = []
    try:
        # Use the processed flag to select unprocessed policies
        cur.execute("""
            SELECT pd.id, pd.policy_title, pd.full_article 
            FROM policy_data pd
            WHERE pd.city_id = %s AND pd.processed = FALSE
            ORDER BY pd.id
            LIMIT %s OFFSET %s
        """, (city_id, batch_size, offset))
        policies = cur.fetchall()
    except Exception as e:
        print(f"Error fetching policies for city {city_id}: {e}")
    finally:
        cur.close()
        conn.close()
    return policies

# Mark policies as processed in the database
def mark_policies_as_processed(policy_ids):
    """Mark a list of policies as processed in the database"""
    if not policy_ids:
        return 0
    
    conn = connect_db()
    cur = conn.cursor()
    try:
        # Convert list of IDs to tuple for SQL IN clause
        if len(policy_ids) == 1:
            # Special case for single ID to avoid syntax error with IN clause
            cur.execute("UPDATE policy_data SET processed = TRUE WHERE id = %s", (policy_ids[0],))
        else:
            placeholders = ','.join(['%s'] * len(policy_ids))
            cur.execute(f"UPDATE policy_data SET processed = TRUE WHERE id IN ({placeholders})", policy_ids)
        
        updated_count = cur.rowcount
        conn.commit()
        print(f"✅ Marked {updated_count} policies as processed")
        return updated_count
    except Exception as e:
        conn.rollback()
        print(f"❌ Error marking policies as processed: {e}")
        return 0
    finally:
        cur.close()
        conn.close()

# Call Gemini to analyze policy batch with enhanced prompt
def analyze_policies_with_gemini(batch_texts, expected_records, city):
    # First check if we're likely rate limited
    if check_rate_limit_status():
        print("⚠️ API rate limit likely reached. Pausing before making a request...")
        time.sleep(60)  # Wait a minute before attempting
    
    # First do a simple test to make sure the API is working
    try:
        test_response = model.generate_content("Hello, this is a test.")
        if not test_response or not test_response.text:
            print("API test failed: empty response")
            return None
    except Exception as e:
        print(f"API test failed: {e}")
        # Check if this is a rate limit error and handle accordingly
        if "429" in str(e) or "quota" in str(e).lower() or "rate limit" in str(e).lower():
            handle_rate_limit_error(e)
            return None
        return None
        
    prompt = f"""
    你是一个专业的中国低碳政策分析专家，擅长对政府政策文本进行客观分析和归类。

    下面提供的是 **{expected_records} 条完整的政策文本**，请详细阅读这些文本，然后分析每条政策，并返回一个 **严格包含 {expected_records} 条记录的 CSV 格式表格**。
    
    注意：政策文本已经在下面的"政策全文"部分提供，不需要再请求政策文本。

    **分析要求：**
    1. **请确保返回完整的 {expected_records} 条记录**，每条政策对应一条分析记录，缺失信息用"未知"填充
    2. **分析必须客观、准确**，基于政策文本内容，不要主观臆断
    3. **对于城市相关性判断：** 请仔细分析政策是否与城市"{city}"相关，包括该政策是否明确针对该城市制定、实施或适用
    4. **对于政策力度评分：** 请基于政策的约束性、资金投入、量化目标等因素综合评定，1分最弱，10分最强

    **返回格式：**
    - CSV 格式，第一行为表头，使用 `"|"` 作为分隔符
    - 直接返回CSV文本，不要添加任何额外解释或代码块标记
    - 第一行必须包含所有字段名称，且与下方列出的字段完全一致
    - 表格必须包含 {expected_records} 行数据（不含表头）

    **分析输出字段：**
    Policy_Title|Is_Valid_Policy|Is_Low_Carbon|Policy_Level|Is_related_to_city|Target_Sectors|Policy_Strength|Policy_Strength_Justification|Issuing_Department|Announcement_Date

    **政策全文：**
    -------------------------
    {batch_texts}
    -------------------------

    请直接返回CSV格式的分析结果，不要添加任何其他说明：
    """

    try:
        response = model.generate_content(prompt)
        
        # Check for empty response
        if not response or not response.text:
            print("❌ Empty response received from API")
            return None
            
        # Print raw response for debugging
        print("\n=== RAW API RESPONSE ===")
        print(response.text)
        print("=== END RAW RESPONSE ===\n")
        
        csv_output = response.text.strip()
        
        # Check if the response contains indications that the API didn't recognize the policy texts
        error_phrases = [
            "请提供",
            "没有提供政策文本",
            "需要政策文本",
            "提供 5 条政策文本",
            "我将按照要求进行分析",
            "我将无法进行分析"
        ]
        
        if any(phrase in csv_output for phrase in error_phrases):
            print("⚠️ API didn't process the policy texts correctly. Response requests policy texts.")
            print("🔄 Modifying prompt format and retrying...")
            
            # Try to solve this by returning None which will trigger a retry with modified prompt
            return None
        
        # Check if the response contains a CSV header (look for column names)
        csv_indicators = ["Policy_Title", "Is_Valid_Policy", "Is_Low_Carbon"]
        has_csv_format = any(indicator in csv_output for indicator in csv_indicators)
        
        if not has_csv_format:
            print("⚠️ Response does not appear to be in CSV format")
            
            # Try to extract tabular data if present (sometimes the API generates tables)
            if "|" in csv_output and "\n" in csv_output:
                print("🔍 Found table-like format, attempting to parse...")
            else:
                print("❌ No CSV or table format detected in response")
                return None

        # Remove Markdown Code Blocks (if they exist)
        if csv_output.startswith("```csv"):
            print("Detected CSV code block, removing markdown formatting")
            csv_output = csv_output.replace("```csv", "").replace("```", "").strip()
        elif csv_output.startswith("```"):
            print("Detected generic code block, removing markdown formatting")
            csv_output = csv_output.replace("```", "").strip()

        # Print formatted output before parsing
        print("=== FORMATTED CSV OUTPUT ===")
        print(csv_output[:200] + "..." if len(csv_output) > 200 else csv_output)
        print("=== END CSV OUTPUT ===\n")

        # Ensure the response is a valid CSV format
        parsed_data = parse_csv_response(csv_output, expected_records)
        
        if parsed_data is None:
            print("❌ Failed to parse CSV response")
            return None

        # Print parsed structure for debugging
        print(f"✅ Successfully parsed {len(parsed_data)} records from CSV")
        
        return parsed_data if len(parsed_data) == expected_records else None

    except Exception as e:
        print(f"❌ Gemini API Error: {e}")
        # Check if this is a rate limit error
        if "429" in str(e) or "quota" in str(e).lower() or "rate limit" in str(e).lower():
            handle_rate_limit_error(e)
        return None

# Convert AI response CSV string into a list of dicts
def parse_csv_response(csv_text, expected_records):
    import csv
    from io import StringIO
    
    lines = csv_text.strip().split("\n")
    
    # Create a CSV reader from the text
    csv_file = StringIO(csv_text)
    reader = csv.DictReader(csv_file, delimiter="|")
    
    parsed_rows = [row for row in reader]

    # Ensure expected number of records exist
    if len(parsed_rows) < expected_records:
        print(f"⚠️ Warning: Gemini returned only {len(parsed_rows)} records instead of {expected_records}.")
        return None  # Force retry in process_batch()

    return parsed_rows

# Add a function to handle rate limit errors
def handle_rate_limit_error(error):
    """Handle rate limit errors by extracting wait time and pausing"""
    error_str = str(error)
    print("⚠️ API quota exceeded or rate limit reached")
    
    # Try to extract retry delay from error message
    retry_seconds = 60  # Default to 60 seconds
    
    # Try to extract seconds from "retry_delay { seconds: XX }" pattern
    match = re.search(r'retry_delay\s*{\s*seconds:\s*(\d+)', error_str)
    if match:
        retry_seconds = int(match.group(1))
        print(f"📊 API suggests waiting {retry_seconds} seconds")
    
    # Add a small random factor to avoid thundering herd problem
    wait_time = retry_seconds + random.randint(5, 15)
    print(f"⏳ Pausing processing for {wait_time} seconds...")
    time.sleep(wait_time)

# Add a function to check rate limit status based on recent errors
def check_rate_limit_status():
    """Check if we're likely experiencing rate limiting based on global state"""
    # This would ideally use a more sophisticated mechanism like a counter in a database
    # For now we'll just return False as a placeholder for the implementation
    return False
    
# Process batch of policies with improved error handling and progressive prompt adjustments
def process_policy_batch(policy_batch, city_name):
    if not policy_batch:
        return 0
    
    policy_ids = [p[0] for p in policy_batch]
    policy_titles = [p[1] for p in policy_batch]
    policy_texts = [p[2] for p in policy_batch]
    
    print(f"🔍 Processing {len(policy_batch)} policies for city {city_name}...")
    
    # Initial batch prompt format
    batch_input = "\n\n".join(
        [f"政策 {i+1}:\n{text}" for i, text in enumerate(policy_texts)]
    )
    
    # Try to get AI analysis
    ai_response = analyze_policies_with_gemini(batch_input, len(policy_batch), city_name)
    
    # Retry logic with exponential backoff and progressive prompt formatting
    retry_count = 0
    base_delay = RETRY_DELAY
    
    while ai_response is None or (isinstance(ai_response, list) and len(ai_response) < len(policy_batch)):
        retry_count += 1
        
        # Calculate exponential backoff with jitter
        delay = min(60, base_delay * (2 ** (retry_count - 1))) + random.randint(0, 5)
        
        if ai_response is None:
            print(f"⚠️ Failed to get valid response. Retrying... ({retry_count}/{MAX_RETRIES})")
            
            # Adjust the prompt format on retries to improve recognition
            if retry_count == 1:
                # First retry: Make policy sections more explicit with clear boundaries
                print("🔄 Retry strategy: Using explicit policy boundaries")
                batch_input = "\n\n".join([
                    f"======= 政策 {i+1} =======\n标题: {title}\n全文:\n{text}\n====================="
                    for i, (title, text) in enumerate(zip(policy_titles, policy_texts))
                ])
            elif retry_count == 2:
                # Second retry: Try with shorter policy excerpts if texts are long
                print("🔄 Retry strategy: Using shortened policy excerpts")
                batch_input = "\n\n".join([
                    f"政策 {i+1} 标题: {title}\n政策 {i+1} 内容摘要: {text[:1000]}..."
                    if len(text) > 1000 else
                    f"政策 {i+1} 标题: {title}\n政策 {i+1} 内容: {text}"
                    for i, (title, text) in enumerate(zip(policy_titles, policy_texts))
                ])
            elif retry_count == 3:
                # Third retry: Try different formatting entirely
                print("🔄 Retry strategy: Using tabular format for policies")
                batch_input = "以下是需要分析的政策列表：\n\n"
                for i, (title, text) in enumerate(zip(policy_titles, policy_texts)):
                    batch_input += f"[政策{i+1}]\n"
                    batch_input += f"标题：{title}\n"
                    batch_input += f"内容：{text[:500]}...\n\n" if len(text) > 500 else f"内容：{text}\n\n"
            elif retry_count == 4:
                # Fourth retry: Process individually instead
                print("🔄 Retry strategy: Switching to individual policy processing")
                return process_policies_individually(policy_batch, city_name)
        else:
            print(f"⚠️ Gemini returned {len(ai_response)} records instead of {len(policy_batch)}. Retrying... ({retry_count}/{MAX_RETRIES})")
            
        if retry_count >= MAX_RETRIES:
            print("❌ Maximum retries reached. Skipping this batch.")
            return 0
            
        print(f"⏳ Waiting {delay} seconds before retry...")
        time.sleep(delay)  # Exponential backoff
        ai_response = analyze_policies_with_gemini(batch_input, len(policy_batch), city_name)
    
    # Save results to database
    success_count = save_results_to_db(policy_ids, ai_response)
    
    # Mark policies as processed if they were saved successfully
    if success_count > 0:
        mark_policies_as_processed(policy_ids)
    
    return success_count

# Add a new function to process policies one by one when batch processing fails
def process_policies_individually(policy_batch, city_name):
    """Process each policy individually when batch processing fails"""
    print(f"🔍 Processing {len(policy_batch)} policies individually for {city_name}...")
    total_success = 0
    
    for index, policy in enumerate(policy_batch):
        policy_id, policy_title, policy_text = policy
        print(f"  🔍 Processing individual policy {index+1}/{len(policy_batch)}: ID {policy_id}")
        
        # Create single policy prompt with very explicit formatting
        single_input = f"""
                    =============================================
                    政策标题: {policy_title}
                    =============================================
                    政策全文:
                    {policy_text}
                    =============================================
                            """
        
        # Try to analyze single policy
        ai_response = analyze_policies_with_gemini(single_input, 1, city_name)
        
        # Basic retry logic for individual policy
        retry_count = 0
        while ai_response is None and retry_count < 2:  # Limit retries for individual policies
            retry_count += 1
            delay = RETRY_DELAY * (2 ** retry_count)
            print(f"  ⚠️ Failed to process individual policy. Retrying... ({retry_count}/2)")
            
            # Try a different format on the retry
            if retry_count == 1:
                single_input = f"""
                    这是一条需要分析的政策：
                    标题：{policy_title}
                    城市：{city_name}
                    内容：
                    {policy_text[:1500]}... (已截断)
                                    """ if len(policy_text) > 1500 else f"""
                    这是一条需要分析的政策：
                    标题：{policy_title}
                    城市：{city_name}
                    内容：
                    {policy_text}
                                    """
            
            time.sleep(delay)
            ai_response = analyze_policies_with_gemini(single_input, 1, city_name)
        
        if ai_response:
            # Save this single result
            success = save_results_to_db([policy_id], ai_response)
            if success:
                mark_policies_as_processed([policy_id])
                total_success += 1
        else:
            print(f"  ❌ Failed to process policy ID {policy_id} after retries")
    
    print(f"✅ Individually processed {total_success}/{len(policy_batch)} policies")
    return total_success

# Save AI processed results to database with improved error handling
def save_results_to_db(policy_ids, ai_responses):
    if not ai_responses:
        print("❌ No valid responses to save to database")
        return 0
        
    conn = connect_db()
    cur = conn.cursor()
    success_count = 0
    
    try:
        for i, response in enumerate(ai_responses):
            policy_id = policy_ids[i]
            
            # Convert string boolean values to Python boolean
            is_valid = str(response.get("Is_Valid_Policy", "False")).lower() == "true"
            is_low_carbon = str(response.get("Is_Low_Carbon", "False")).lower() == "true"
            is_related_to_city = str(response.get("Is_related_to_city", "False")).lower() == "true"
            
            # Convert policy strength to integer
            try:
                policy_strength = int(response.get("Policy_Strength", "0"))
            except ValueError:
                policy_strength = 0
            
            # Convert announcement date to proper format
            announcement_date = None
            date_str = response.get("Announcement_Date", "")
            if date_str:
                try:
                    # Try to parse the date string
                    announcement_date = datetime.strptime(date_str, "%Y-%m-%d").date()
                except ValueError:
                    # Leave as None if parsing fails
                    pass
            
            # Insert processed data
            cur.execute(f"""
                INSERT INTO {PROCESSED_TABLE} 
                (id, policy_title, is_valid_policy, is_low_carbon, policy_level, 
                is_related_to_city, target_sectors, policy_strength, 
                policy_strength_justification, issuing_department, announcement_date)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                policy_title = EXCLUDED.policy_title,
                is_valid_policy = EXCLUDED.is_valid_policy,
                is_low_carbon = EXCLUDED.is_low_carbon,
                policy_level = EXCLUDED.policy_level,
                is_related_to_city = EXCLUDED.is_related_to_city,
                target_sectors = EXCLUDED.target_sectors,
                policy_strength = EXCLUDED.policy_strength,
                policy_strength_justification = EXCLUDED.policy_strength_justification,
                issuing_department = EXCLUDED.issuing_department,
                announcement_date = EXCLUDED.announcement_date,
                processed_at = CURRENT_TIMESTAMP
            """, (
                policy_id,
                response.get("Policy_Title", ""),
                is_valid,
                is_low_carbon,
                response.get("Policy_Level", ""),
                is_related_to_city,
                response.get("Target_Sectors", ""),
                policy_strength,
                response.get("Policy_Strength_Justification", ""),
                response.get("Issuing_Department", ""),
                announcement_date
            ))
            success_count += 1
        
        conn.commit()
        print(f"✅ Successfully saved {success_count} processed policies to database")
    except Exception as e:
        conn.rollback()
        print(f"❌ Error saving results to database: {e}")
    finally:
        cur.close()
        conn.close()
    
    return success_count

# Process all policies for a city
def process_city(city_id, city_name):
    print(f"🏙️ Processing policies for city: {city_name} (ID: {city_id})")
    total_processed = 0
    offset = 0
    
    while True:
        # Get a batch of unprocessed policies
        policies = get_city_policies(city_id, offset, BATCH_SIZE)
        
        if not policies:
            break  # No more unprocessed policies
        
        # Process the batch
        processed_count = process_policy_batch(policies, city_name)
        total_processed += processed_count
        
        # Update offset for next batch only if no records processed
        # This ensures we don't skip any records if some failed to process
        if processed_count == 0:
            offset += BATCH_SIZE
        
        # Small delay to prevent API rate limiting
        time.sleep(1)
    
    print(f"✅ Completed processing for {city_name}: {total_processed} policies processed")
    return total_processed

# Add a function to reset processed flags (useful for reprocessing)
def reset_processed_flags(city_id=None):
    """Reset processed flags to False, optionally for a specific city"""
    conn = connect_db()
    cur = conn.cursor()
    try:
        if city_id:
            cur.execute("UPDATE policy_data SET processed = FALSE WHERE city_id = %s", (city_id,))
            print(f"Reset processed flags for city ID {city_id}")
        else:
            cur.execute("UPDATE policy_data SET processed = FALSE")
            print("Reset all processed flags")
        
        reset_count = cur.rowcount
        conn.commit()
        print(f"Reset {reset_count} records")
        return reset_count
    except Exception as e:
        conn.rollback()
        print(f"Error resetting processed flags: {e}")
    finally:
        cur.close()
        conn.close()

# Main function with API key verification and quota handling
def main():
    # Verify API key is working before continuing
    try:
        test_response = model.generate_content("Test API connection.")
        print("✅ API connection test successful")
    except Exception as e:
        print(f"❌ API connection test failed: {e}")
        
        # Check if this is a rate limit error
        if "429" in str(e) or "quota" in str(e).lower():
            print("⚠️ API quota exceeded or rate limit reached. You may need to wait before processing.")
            sys.exit(2)  # Special exit code for quota issues
        else:
            print("Please check your API key and internet connection")
            sys.exit(1)
        
    # Create the processed table if it doesn't exist
    create_processed_table()
    
    # Get cities to process
    cities = get_cities()
    
    # Keep track of progress
    total_policies_processed = 0
    
    # Process each city
    for city_id, city_name in cities:
        try:
            policies_processed = process_city(city_id, city_name)
            total_policies_processed += policies_processed
        except KeyboardInterrupt:
            print("\n⚠️ Processing interrupted. Progress has been saved.")
            sys.exit(0)
        except Exception as e:
            print(f"❌ Error processing city {city_name} (ID: {city_id}): {e}")
            continue
    
    print(f"🎉 Processing complete! Total policies processed: {total_policies_processed}")

# Update script with additional command-line options for resetting flags
if __name__ == "__main__":
    # Check for reset flag option
    if len(sys.argv) > 1 and sys.argv[1] == "--reset":
        if len(sys.argv) > 2:
            try:
                city_id = int(sys.argv[2])
                reset_processed_flags(city_id)
            except ValueError:
                print("Please provide a valid city ID as an integer.")
        else:
            # Reset all flags if no city ID provided
            reset_processed_flags()
        sys.exit(0)
        
    # Check if we should process a specific city
    if len(sys.argv) > 1:
        try:
            city_id = int(sys.argv[1])
            conn = connect_db()
            cur = conn.cursor()
            cur.execute("SELECT city FROM policy_cities WHERE id = %s", (city_id,))
            result = cur.fetchone()
            if result:
                city_name = result[0]
                print(f"Processing single city: {city_name} (ID: {city_id})")
                create_processed_table()
                process_city(city_id, city_name)
            else:
                print(f"City with ID {city_id} not found.")
            cur.close()
            conn.close()
        except ValueError:
            print("Please provide a valid city ID as an integer.")
    else:
        # Process all cities
        main()
