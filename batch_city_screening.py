#!/usr/bin/env python3
"""
批量城市政策筛选程序
支持指定城市范围、断点续传等功能
"""

import os
import sys
import json
import time
import logging
import pandas as pd
import psycopg2
import google.generativeai as genai
from datetime import datetime
import argparse

# 配置日志
def setup_logging(log_file="batch_city_screening.log"):
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

# 加载配置
CONFIG_FILE = "policy_config.json"

try:
    with open(CONFIG_FILE, "r") as f:
        config = json.load(f)
    
    DB_CONFIG = config["database"]
    AI_CONFIG = config["ai"]
    GEMINI_API_KEYS = AI_CONFIG["gemini_api_keys"]
    AI_MODEL = AI_CONFIG["model"]
    MAX_RETRIES = AI_CONFIG["max_retries"]
    RETRY_DELAY = AI_CONFIG["retry_delay"]
    RATE_LIMIT_PER_MINUTE = AI_CONFIG["rate_limit_per_minute"]
    BATCH_SIZE = AI_CONFIG["batch_size"]
    
    logger.info("✅ Configuration loaded successfully")
except Exception as e:
    logger.error(f"❌ Error loading configuration: {e}")
    sys.exit(1)

# API密钥管理
current_api_key_index = 0

def configure_gemini():
    """配置Gemini API"""
    global current_api_key_index
    try:
        api_key = GEMINI_API_KEYS[current_api_key_index]
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel(AI_MODEL)
        logger.info(f"✅ Gemini API configured with key {current_api_key_index + 1}/{len(GEMINI_API_KEYS)}")
        return model
    except Exception as e:
        logger.error(f"❌ Error configuring Gemini API: {e}")
        return None

def switch_api_key():
    """切换到下一个API密钥"""
    global current_api_key_index
    current_api_key_index = (current_api_key_index + 1) % len(GEMINI_API_KEYS)
    logger.info(f"🔄 Switching to API key {current_api_key_index + 1}/{len(GEMINI_API_KEYS)}")
    return configure_gemini()

model = configure_gemini()
if not model:
    logger.error("❌ Failed to initialize Gemini model")
    sys.exit(1)

def connect_database():
    """连接PostgreSQL数据库"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        logger.info("✅ Database connection established")
        return conn
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return None

def get_cities_to_process(conn, start_from=None, limit=None, city_pattern=None, include_processed=False):
    """获取需要处理的城市列表"""
    try:
        base_query = """
        SELECT city, COUNT(*) as policy_count,
               SUM(CASE WHEN should_be_deleted IS NOT NULL THEN 1 ELSE 0 END) as processed_count
        FROM raw_processed_policies
        WHERE city IS NOT NULL AND city <> ''
        """

        params = []
        if city_pattern:
            base_query += " AND city LIKE %s"
            params.append(f"%{city_pattern}%")

        base_query += " GROUP BY city"

        if not include_processed:
            base_query += " HAVING SUM(CASE WHEN should_be_deleted IS NOT NULL THEN 1 ELSE 0 END) = 0"

        base_query += " ORDER BY policy_count ASC"

        if limit:
            base_query += f" LIMIT {limit}"
        if start_from:
            base_query += f" OFFSET {start_from}"

        df = pd.read_sql(base_query, conn, params=params)
        cities = df['city'].tolist()

        logger.info(f"📍 Found {len(cities)} cities to process")
        for _, row in df.iterrows():
            logger.info(f"  {row['city']}: {row['policy_count']} policies ({row['processed_count']} processed)")
        return cities
    except Exception as e:
        logger.error(f"❌ Failed to get cities: {e}")
        return []

def get_city_policies(conn, city):
    """获取指定城市的未处理政策数据"""
    try:
        query = """
        SELECT policy_id, policy_title, policy_level, announce_date, 
               issuing_department, city, policy_type
        FROM raw_processed_policies
        WHERE city = %s
        AND should_be_deleted IS NULL
        ORDER BY policy_id
        """
        df = pd.read_sql(query, conn, params=(city,))
        logger.info(f"📊 Found {len(df)} unprocessed policies for {city}")
        return df
    except Exception as e:
        logger.error(f"❌ Failed to get policies for {city}: {e}")
        return pd.DataFrame()

def create_prompt(city, policies_df):
    """创建发送给Gemini的提示词"""
    prompt = f"""你是一名中国政策分析专家。请根据下述规则，对输入的每条政策数据，输出一行：policy_id,true/false,具体理由。 

规则如下：
1. 如果政策标题、内容或发布机构只针对{city}以外的城市/区域（如"深圳市"、"东莞市"、"京津冀"等），且与{city}无关，输出true，并注明"仅适用于XX市/地区，与{city}无关"；
2. 如多条记录为同一政策的不同版本、目录、文号、正文片段，仅保留最权威、最正式、最完整一条，其余均输出true，理由为"同一政策仅保留最权威、最正式版本，其余为重复"；
3. 只要政策适用于{city}或与{city}直接相关，输出false，理由为"保留"；
4. 如政策仅针对{city}没有的行业/试点/特殊区域，且本地无实际落地，输出true，理由写"仅针对XX行业/区域，{city}无相关落地"；
5. 不能机械比对，必须结合内容、政策文本、实际落地、政策部门和中国行政常识作专业判断。

每行只输出一条结果，按"policy_id,true/false,理由"格式，不输出任何多余内容或解释。

以下是需要分析的政策数据：

"""
    
    for _, row in policies_df.iterrows():
        policy_info = f"policy_id: {row['policy_id']}, "
        policy_info += f"标题: {row['policy_title'] or '无标题'}, "
        policy_info += f"发布机构: {row['issuing_department'] or '未知'}, "
        policy_info += f"政策层级: {row['policy_level'] or '未知'}, "
        policy_info += f"发布日期: {row['announce_date'] or '未知'}, "
        policy_info += f"政策类型: {row['policy_type'] or '未知'}"
        prompt += policy_info + "\n"
    
    return prompt

def call_gemini_api(prompt, retries=0):
    """调用Gemini API"""
    global model
    
    try:
        response = model.generate_content(prompt)
        if response.text:
            return response.text.strip()
        else:
            logger.warning("⚠️ Empty response from Gemini API")
            return None
    except Exception as e:
        logger.error(f"❌ Gemini API call failed: {e}")
        
        if retries < MAX_RETRIES:
            if "quota" in str(e).lower() or "rate" in str(e).lower():
                # 尝试切换API密钥
                model = switch_api_key()
                if model:
                    time.sleep(RETRY_DELAY)
                    return call_gemini_api(prompt, retries + 1)
            else:
                time.sleep(RETRY_DELAY * (retries + 1))
                return call_gemini_api(prompt, retries + 1)
        
        return None

def parse_ai_response(response_text):
    """解析AI响应"""
    results = []
    if not response_text:
        return results
    
    lines = [line.strip() for line in response_text.strip().split('\n')
             if line.strip() and 'policy_id' not in line.lower() and not line.startswith('示例')]
    
    for line in lines:
        try:
            if ',' in line:
                parts = line.split(',', 2)
                if len(parts) >= 3:
                    policy_id = int(parts[0].strip())
                    should_delete = parts[1].strip().lower() == 'true'
                    reason = parts[2].strip()
                    results.append((policy_id, should_delete, reason))
        except (ValueError, IndexError) as e:
            logger.warning(f"⚠️ Failed to parse line: {line} - {e}")
            continue
    
    return results

def update_database(conn, results):
    """更新数据库"""
    try:
        with conn.cursor() as cur:
            for policy_id, should_delete, reason in results:
                cur.execute("""
                    UPDATE raw_processed_policies
                    SET should_be_deleted = %s, delete_reason = %s
                    WHERE policy_id = %s
                """, (should_delete, reason, policy_id))
            conn.commit()
        logger.info(f"✅ Updated {len(results)} records in database")
        return True
    except Exception as e:
        logger.error(f"❌ Database update failed: {e}")
        conn.rollback()
        return False

def process_city(conn, city):
    """处理单个城市的政策数据"""
    logger.info(f"🏙️ Processing city: {city}")
    
    # 获取城市政策数据
    policies_df = get_city_policies(conn, city)
    if policies_df.empty:
        logger.warning(f"⚠️ No unprocessed policies found for {city}")
        return True  # 返回True表示该城市已完成
    
    # 分批处理
    total_policies = len(policies_df)
    processed_count = 0
    
    for start_idx in range(0, total_policies, BATCH_SIZE):
        end_idx = min(start_idx + BATCH_SIZE, total_policies)
        batch_df = policies_df.iloc[start_idx:end_idx]
        
        logger.info(f"📝 Processing batch {start_idx+1}-{end_idx} of {total_policies} policies for {city}")
        
        # 创建提示词
        prompt = create_prompt(city, batch_df)
        
        # 调用Gemini API
        response = call_gemini_api(prompt)
        if not response:
            logger.error(f"❌ Failed to get response for {city} batch {start_idx+1}-{end_idx}")
            return False
        
        # 解析响应
        results = parse_ai_response(response)
        if not results:
            logger.warning(f"⚠️ No valid results parsed for {city} batch {start_idx+1}-{end_idx}")
            continue
        
        # 更新数据库
        if update_database(conn, results):
            processed_count += len(results)
            logger.info(f"✅ Successfully processed {len(results)} policies for {city}")
        else:
            return False
        
        # 速率限制
        time.sleep(60 / RATE_LIMIT_PER_MINUTE)
    
    logger.info(f"🎉 Completed processing {city}: {processed_count}/{total_policies} policies processed")
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="批量城市政策筛选程序")
    parser.add_argument("--start", type=int, default=0, help="起始城市索引")
    parser.add_argument("--limit", type=int, help="处理城市数量限制")
    parser.add_argument("--pattern", type=str, help="城市名称模式匹配")
    parser.add_argument("--dry-run", action="store_true", help="仅显示将要处理的城市，不实际处理")
    parser.add_argument("--include-processed", action="store_true", help="包含已处理的城市")

    args = parser.parse_args()
    
    logger.info("🚀 Starting Batch City Policy Screening System")
    logger.info(f"📋 Parameters: start={args.start}, limit={args.limit}, pattern={args.pattern}, dry_run={args.dry_run}, include_processed={args.include_processed}")

    # 连接数据库
    conn = connect_database()
    if not conn:
        return

    try:
        # 获取需要处理的城市
        cities = get_cities_to_process(conn, args.start, args.limit, args.pattern, args.include_processed)
        if not cities:
            logger.warning("⚠️ No cities to process")
            return
        
        if args.dry_run:
            logger.info("🔍 Dry run mode - cities that would be processed:")
            for i, city in enumerate(cities):
                logger.info(f"  {i+1}. {city}")
            return
        
        # 处理每个城市
        total_cities = len(cities)
        processed_cities = 0
        failed_cities = []
        
        for i, city in enumerate(cities, 1):
            logger.info(f"🔄 Processing city {i}/{total_cities}: {city}")
            
            try:
                if process_city(conn, city):
                    processed_cities += 1
                else:
                    failed_cities.append(city)
                    logger.error(f"❌ Failed to process {city}")
            except KeyboardInterrupt:
                logger.info("⏹️ Processing interrupted by user")
                break
            except Exception as e:
                logger.error(f"❌ Unexpected error processing {city}: {e}")
                failed_cities.append(city)
            
            # 城市间的延迟
            if i < total_cities:
                time.sleep(2)
        
        # 总结
        logger.info(f"🎉 Processing completed:")
        logger.info(f"  ✅ Successfully processed: {processed_cities}/{total_cities} cities")
        if failed_cities:
            logger.info(f"  ❌ Failed cities: {', '.join(failed_cities)}")
        
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
    finally:
        conn.close()
        logger.info("🔚 Database connection closed")

if __name__ == "__main__":
    main()
