#!/usr/bin/env python3
"""
智能批量政策筛选程序
按政策数量排序，优先处理小城市，跳过过大的城市
"""

import os
import sys
import json
import time
import logging
import pandas as pd
import psycopg2
import argparse
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('smart_batch_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 加载配置
CONFIG_FILE = "policy_config.json"

try:
    with open(CONFIG_FILE, "r") as f:
        config = json.load(f)
    
    DB_CONFIG = config["database"]
    logger.info("✅ Configuration loaded successfully")
except Exception as e:
    logger.error(f"❌ Error loading configuration: {e}")
    sys.exit(1)

def connect_database():
    """连接PostgreSQL数据库"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        logger.info("✅ Database connection established")
        return conn
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return None

def get_cities_by_size(conn, max_policies=1000):
    """获取按政策数量排序的城市列表，过滤掉过大的城市"""
    try:
        query = """
        SELECT city, COUNT(*) as policy_count,
               SUM(CASE WHEN should_be_deleted IS NOT NULL THEN 1 ELSE 0 END) as processed_count
        FROM raw_processed_policies
        WHERE city IS NOT NULL AND city <> ''
        GROUP BY city
        HAVING SUM(CASE WHEN should_be_deleted IS NOT NULL THEN 1 ELSE 0 END) = 0
        AND COUNT(*) <= %s
        ORDER BY policy_count ASC
        """
        
        df = pd.read_sql(query, conn, params=(max_policies,))
        
        logger.info(f"📊 Found {len(df)} cities with ≤{max_policies} policies to process")
        
        # 统计被跳过的大城市
        skip_query = """
        SELECT city, COUNT(*) as policy_count
        FROM raw_processed_policies
        WHERE city IS NOT NULL AND city <> ''
        GROUP BY city
        HAVING SUM(CASE WHEN should_be_deleted IS NOT NULL THEN 1 ELSE 0 END) = 0
        AND COUNT(*) > %s
        ORDER BY policy_count DESC
        """
        
        skip_df = pd.read_sql(skip_query, conn, params=(max_policies,))
        if not skip_df.empty:
            logger.info(f"⚠️ Skipping {len(skip_df)} large cities (>{max_policies} policies):")
            for _, row in skip_df.head(5).iterrows():
                logger.info(f"  {row['city']}: {row['policy_count']} policies")
            if len(skip_df) > 5:
                logger.info(f"  ... and {len(skip_df) - 5} more")
        
        return df
    except Exception as e:
        logger.error(f"❌ Failed to get cities: {e}")
        return pd.DataFrame()

def process_cities_batch(cities_df, batch_size=5):
    """批量处理城市"""
    total_cities = len(cities_df)
    processed_count = 0
    failed_cities = []
    
    for i in range(0, total_cities, batch_size):
        batch_cities = cities_df.iloc[i:i+batch_size]
        
        logger.info(f"🔄 Processing batch {i//batch_size + 1}: cities {i+1}-{min(i+batch_size, total_cities)} of {total_cities}")
        
        for _, row in batch_cities.iterrows():
            city = row['city']
            policy_count = row['policy_count']
            
            logger.info(f"🏙️ Processing {city} ({policy_count} policies)")
            
            # 调用批量处理程序处理单个城市
            cmd = f'python batch_city_screening.py --pattern "{city}"'
            start_time = time.time()
            
            exit_code = os.system(cmd)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if exit_code == 0:
                processed_count += 1
                logger.info(f"✅ {city} processed successfully in {duration:.1f}s")
            else:
                failed_cities.append(city)
                logger.error(f"❌ {city} failed to process")
            
            # 城市间延迟
            time.sleep(2)
        
        # 批次间延迟
        if i + batch_size < total_cities:
            logger.info("⏳ Waiting 10 seconds before next batch...")
            time.sleep(10)
    
    return processed_count, failed_cities

def get_processing_stats(conn):
    """获取处理统计信息"""
    try:
        query = """
        SELECT 
            COUNT(DISTINCT city) as total_cities,
            COUNT(DISTINCT CASE WHEN should_be_deleted IS NOT NULL THEN city END) as processed_cities,
            COUNT(*) as total_policies,
            SUM(CASE WHEN should_be_deleted IS NOT NULL THEN 1 ELSE 0 END) as processed_policies,
            SUM(CASE WHEN should_be_deleted = true THEN 1 ELSE 0 END) as to_delete,
            SUM(CASE WHEN should_be_deleted = false THEN 1 ELSE 0 END) as to_keep
        FROM raw_processed_policies 
        WHERE city IS NOT NULL AND city <> ''
        """
        
        with conn.cursor() as cur:
            cur.execute(query)
            result = cur.fetchone()
        
        return {
            'total_cities': result[0],
            'processed_cities': result[1],
            'total_policies': result[2],
            'processed_policies': result[3],
            'to_delete': result[4] or 0,
            'to_keep': result[5] or 0
        }
    except Exception as e:
        logger.error(f"❌ Failed to get statistics: {e}")
        return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="智能批量政策筛选程序")
    parser.add_argument("--max-policies", type=int, default=1000, help="单个城市最大政策数量限制")
    parser.add_argument("--batch-size", type=int, default=5, help="批次大小")
    parser.add_argument("--dry-run", action="store_true", help="仅显示将要处理的城市")
    parser.add_argument("--stats", action="store_true", help="显示统计信息")
    
    args = parser.parse_args()
    
    logger.info("🚀 Starting Smart Batch Policy Screening System")
    logger.info(f"📋 Parameters: max_policies={args.max_policies}, batch_size={args.batch_size}, dry_run={args.dry_run}")
    
    # 连接数据库
    conn = connect_database()
    if not conn:
        return
    
    try:
        if args.stats:
            stats = get_processing_stats(conn)
            if stats:
                logger.info("📊 Current Processing Statistics:")
                logger.info(f"  Cities: {stats['processed_cities']}/{stats['total_cities']} processed")
                logger.info(f"  Policies: {stats['processed_policies']}/{stats['total_policies']} processed")
                if stats['processed_policies'] > 0:
                    delete_rate = (stats['to_delete'] / stats['processed_policies']) * 100
                    keep_rate = (stats['to_keep'] / stats['processed_policies']) * 100
                    logger.info(f"  Results: {stats['to_delete']} to delete ({delete_rate:.1f}%), {stats['to_keep']} to keep ({keep_rate:.1f}%)")
            return
        
        # 获取要处理的城市
        cities_df = get_cities_by_size(conn, args.max_policies)
        if cities_df.empty:
            logger.info("✅ No cities to process (all small cities completed)")
            return
        
        if args.dry_run:
            logger.info(f"🔍 Dry run mode - cities that would be processed:")
            for i, (_, row) in enumerate(cities_df.iterrows(), 1):
                logger.info(f"  {i}. {row['city']}: {row['policy_count']} policies")
            return
        
        # 显示开始统计
        start_stats = get_processing_stats(conn)
        if start_stats:
            logger.info(f"📊 Starting with {start_stats['processed_cities']}/{start_stats['total_cities']} cities processed")
        
        # 开始处理
        start_time = datetime.now()
        processed_count, failed_cities = process_cities_batch(cities_df, args.batch_size)
        end_time = datetime.now()
        
        # 显示结果
        duration = end_time - start_time
        logger.info(f"🎉 Processing completed in {duration}")
        logger.info(f"✅ Successfully processed: {processed_count}/{len(cities_df)} cities")
        
        if failed_cities:
            logger.info(f"❌ Failed cities: {', '.join(failed_cities)}")
        
        # 显示最终统计
        final_stats = get_processing_stats(conn)
        if final_stats:
            logger.info(f"📊 Final statistics: {final_stats['processed_cities']}/{final_stats['total_cities']} cities processed")
            progress = (final_stats['processed_policies'] / final_stats['total_policies']) * 100
            logger.info(f"📈 Overall progress: {progress:.2f}%")
    
    except KeyboardInterrupt:
        logger.info("⏹️ Processing interrupted by user")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
    finally:
        conn.close()
        logger.info("🔚 Database connection closed")

if __name__ == "__main__":
    main()
