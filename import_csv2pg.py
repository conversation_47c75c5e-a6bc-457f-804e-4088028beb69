import os
import json
import shutil
import pandas as pd
import psycopg2
import re
import sys

# Load configuration from policy_config.json
CONFIG_FILE = "/opt/app/PolicyDW/policy_config.json"

with open(CONFIG_FILE, "r") as f:
    config = json.load(f)

# PostgreSQL Connection Details
DB_CONFIG = config["database"]
DATA_FOLDER = config["paths"]["data_folder"]
LOADED_FOLDER = config["paths"]["loaded_folder"]
TEMP_CSV = config["paths"]["temp_csv"]
TABLE_NAME = config["table_name"]

# Ensure "loaded/" folder exists
os.makedirs(LOADED_FOLDER, exist_ok=True)

# PostgreSQL Connection Function
def connect_db():
    return psycopg2.connect(
        dbname=DB_CONFIG["dbname"],
        user=DB_CONFIG["user"],
        password=DB_CONFIG["password"],
        host=DB_CONFIG["host"],
        port=DB_CONFIG["port"]
    )

# Convert Excel Files to a Single CSV and Move Files to "loaded/"
def excel_to_csv(folder_path, csv_path, loaded_folder):
    all_data = []
    expected_columns = 12  # Updated to 12 columns to match PostgreSQL table
    files_processed = 0

    for file in os.listdir(folder_path):
        if file.endswith(".xlsx") or file.endswith(".xls"):
            file_path = os.path.join(folder_path, file)
            processed = False
            
            # First try with standard engines
            engines_to_try = ['openpyxl', 'xlrd', 'odf']
            for engine in engines_to_try:
                try:
                    print(f"Attempting to read {file} with {engine} engine...")
                    df = pd.read_excel(file_path, dtype=str, engine=engine)
                    print(f"Successfully read {file} with {engine} engine")
                    
                    if df.shape[1] != expected_columns:
                        print(f"⚠️ Warning: {file} has {df.shape[1]} columns, expected {expected_columns}. Skipping...")
                        break
                    
                    all_data.append(df)
                    shutil.move(file_path, os.path.join(loaded_folder, file))
                    print(f"Moved {file} to {loaded_folder}")
                    processed = True
                    break
                except Exception as e:
                    print(f"Error with {engine} engine for {file}: {e}")
            
            # If all engines failed, try to fix and then try CSV approach
            if not processed:
                # Try to fix the file
                fix_excel_file(file_path)
                
                # Try to read again after fixing
                for engine in engines_to_try:
                    try:
                        print(f"Retrying {file} with {engine} engine after fix...")
                        df = pd.read_excel(file_path, dtype=str, engine=engine)
                        print(f"Successfully read fixed {file}")
                        
                        if df.shape[1] != expected_columns:
                            print(f"⚠️ Fixed file has incorrect columns: {df.shape[1]}, expected {expected_columns}")
                            continue
                        
                        all_data.append(df)
                        shutil.move(file_path, os.path.join(loaded_folder, file))
                        print(f"Moved fixed {file} to {loaded_folder}")
                        processed = True
                        break
                    except Exception as e:
                        print(f"Error reading fixed file with {engine}: {e}")
                
                # Last resort: Try as CSV
                if not processed:
                    df = try_as_csv(file_path, expected_columns)
                    if df is not None and df.shape[1] >= expected_columns:
                        all_data.append(df)
                        shutil.move(file_path, os.path.join(loaded_folder, file))
                        print(f"Moved {file} to {loaded_folder} after CSV processing")
                        processed = True
            
            if not processed:
                print(f"⚠️ Failed to process {file} with all available methods. Skipping file.")

    if all_data:
        final_df = pd.concat(all_data, ignore_index=True)
        
        # Print column count and row count for verification
        print(f"Final dataframe has {final_df.shape[1]} columns and {len(final_df)} rows")
        
        # Print sample data for verification
        print("Sample data from first few rows:")
        print(final_df.head(3))
        
        # Get column names from the database table for column mapping
        conn = connect_db()
        cur = conn.cursor()
        try:
            cur.execute(f"SELECT * FROM {TABLE_NAME} LIMIT 0")
            column_names = [desc[0] for desc in cur.description]
            column_types = {}
            
            # Get column types
            cur.execute(f"""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = '{TABLE_NAME}'
            """)
            for col, dtype in cur.fetchall():
                column_types[col] = dtype
                
            print(f"Database column types: {column_types}")
            
            # Format date columns properly
            for i, col in enumerate(column_names):
                if i < len(final_df.columns) and column_types.get(col) == 'date':
                    print(f"Converting column {i} ({col}) to date format")
                    # Convert year values to ISO date format (YYYY-01-01)
                    final_df.iloc[:, i] = final_df.iloc[:, i].apply(
                        lambda x: convert_to_iso_date(x)
                    )
                    
        except Exception as e:
            print(f"Error getting column info: {e}")
        finally:
            cur.close()
            conn.close()
        
        # Save CSV using comma as delimiter and without headers
        final_df.to_csv(csv_path, index=False, header=False, sep=",", na_rep="", quoting=1)
        print(f"Saved CSV file at {csv_path} with {len(final_df)} rows")
        
        # Verify the CSV file was created and has content
        if os.path.exists(csv_path):
            file_size = os.path.getsize(csv_path)
            print(f"CSV file size: {file_size} bytes")
            if file_size == 0:
                print("WARNING: CSV file is empty!")
        else:
            print("ERROR: CSV file was not created!")
    else:
        print("No data was processed from Excel files. CSV not created.")
        return False
        
    return len(all_data) > 0

# Enhanced function to handle various date formats
def convert_to_iso_date(date_str):
    """Convert various date formats to ISO format (YYYY-MM-DD)"""
    date_str = validate_date_string(date_str)
    if date_str is None:
        return None
    
    # Handle simple year format: "2020" -> "2020-01-01"
    if date_str.isdigit() and len(date_str) == 4:
        return f"{date_str}-01-01"
        
    # Handle year.month format: "2020.07" -> "2020-07-01"
    if re.match(r'^\d{4}\.\d{1,2}$', date_str):
        year, month = date_str.split('.')
        month = month.zfill(2)  # Ensure month is two digits
        return f"{year}-{month}-01"
        
    # Handle year-month format: "2020-07" -> "2020-07-01"
    if re.match(r'^\d{4}-\d{1,2}$', date_str):
        year, month = date_str.split('-')
        month = month.zfill(2)
        return f"{year}-{month}-01"
        
    # Handle Chinese date format: "二〇二〇年七月" -> approximate to "2020-07-01"
    if '年' in date_str and ('月' in date_str or '日' in date_str):
        # This is a simplification - would need more complex logic for accurate conversion
        try:
            # Extract year digits if they look like numbers
            year_match = re.search(r'(\d{4})年', date_str)
            if year_match:
                return f"{year_match.group(1)}-01-01"
            # For Chinese numerals, default to current year
            return f"{pd.Timestamp.now().year}-01-01"
        except:
            return f"{pd.Timestamp.now().year}-01-01"  # Default to current year
            
    # If it already looks like a valid date, return as is
    if re.match(r'^\d{4}-\d{2}-\d{2}$', date_str):
        return date_str
        
    # For any other format, log and return NULL
    print(f"Warning: Unrecognized date format: {date_str}")
    return None

# New function to process CSV files with proper date formatting
def preprocess_csv_for_import(csv_path, table_name):
    """Process CSV file to ensure date columns are properly formatted"""
    print(f"Preprocessing CSV file {csv_path} for import...")
    
    # Get column types from database
    conn = connect_db()
    cur = conn.cursor()
    column_types = {}
    date_column_indexes = []
    
    try:
        # Get column names and types
        cur.execute(f"SELECT * FROM {table_name} LIMIT 0")
        column_names = [desc[0] for desc in cur.description]
        
        cur.execute(f"""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = '{table_name}'
        """)
        
        # Build mapping of column names to data types
        for col, dtype in cur.fetchall():
            column_types[col] = dtype
            
        print(f"Column types from database: {column_types}")
        
        # Identify date column indexes
        for i, col in enumerate(column_names):
            if column_types.get(col) == 'date':
                date_column_indexes.append(i)
                print(f"Column {i} ({col}) is a date column")
    finally:
        cur.close()
        conn.close()
    
    if not date_column_indexes:
        print("No date columns found, skipping preprocessing")
        return
        
    # Read CSV, fix dates, and write back
    print("Reading CSV data...")
    df = pd.read_csv(csv_path, header=None, dtype=str)
    
    print(f"CSV contains {len(df)} rows and {len(df.columns)} columns")
    
    # Convert date columns to proper format
    for idx in date_column_indexes:
        if idx < len(df.columns):
            print(f"Converting column {idx} to proper date format...")
            # Use enhanced date converter
            df[idx] = df[idx].apply(convert_to_iso_date)
            # Show sample of converted data
            print(f"Sample of converted data in column {idx}:")
            print(df[idx].head(3))
    
    # Save the preprocessed CSV file
    temp_file = csv_path + ".tmp"
    df.to_csv(temp_file, index=False, header=False, quoting=1)
    
    # Replace original file with processed one
    os.replace(temp_file, csv_path)
    print(f"Preprocessing complete, updated {csv_path}")

# New function to repair problematic CSV data
def repair_csv_file(csv_path, expected_columns):
    """Attempt to repair a CSV file with potential formatting issues"""
    print(f"Repairing CSV file {csv_path}...")
    
    # Read the file using pandas with more lenient parsing
    try:
        # First try with standard CSV reader but more error tolerance
        df = pd.read_csv(csv_path, header=None, dtype=str, 
                        quotechar='"', escapechar='\\', 
                        on_bad_lines='warn', engine='python')
        
        # Check if we have the expected number of columns
        if len(df.columns) != expected_columns:
            print(f"CSV has {len(df.columns)} columns, expected {expected_columns}")
            
            # Try alternate reading method - read raw lines and process manually
            with open(csv_path, 'r', encoding='utf-8', errors='replace') as f:
                lines = f.readlines()
                
            fixed_lines = []
            for i, line in enumerate(lines, 1):
                fields = []
                in_quotes = False
                current_field = ""
                
                # Manual CSV parsing to handle quoted fields with commas
                for char in line:
                    if char == '"':
                        in_quotes = not in_quotes
                        current_field += char
                    elif char == ',' and not in_quotes:
                        fields.append(current_field)
                        current_field = ""
                    else:
                        current_field += char
                        
                # Add the last field
                if current_field:
                    fields.append(current_field)
                
                # Check if we have the right number of fields
                if len(fields) != expected_columns:
                    print(f"Line {i}: Found {len(fields)} fields, expected {expected_columns}")
                    # Pad with empty fields if needed
                    while len(fields) < expected_columns:
                        fields.append("")
                    # Truncate if too many
                    if len(fields) > expected_columns:
                        fields = fields[:expected_columns]
                
                # Rebuild the CSV line with proper quoting
                fixed_line = ",".join([f'"{field.strip('"')}"' for field in fields])
                fixed_lines.append(fixed_line)
            
            # Write fixed lines back to a new file
            temp_file = csv_path + ".fixed"
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write("\n".join(fixed_lines))
                
            # Replace original with fixed file
            os.replace(temp_file, csv_path)
            print(f"Repaired CSV file and fixed {len(lines)} lines")
        else:
            print(f"CSV has the correct number of columns ({expected_columns})")
            
    except Exception as e:
        print(f"Error repairing CSV: {e}")
        return False
        
    return True

# Load CSV to PostgreSQL Using \copy
def load_csv_to_postgresql(csv_path, table_name, preprocess=True):
    # Check if file exists and has content
    if not os.path.exists(csv_path):
        print(f"Error: CSV file '{csv_path}' does not exist.")
        return False
        
    file_size = os.path.getsize(csv_path)
    if file_size == 0:
        print(f"Error: CSV file '{csv_path}' exists but is empty (0 bytes).")
        return False
    
    # Get column mapping information from database
    conn = connect_db()
    cur = conn.cursor()
    column_info = {}
    date_columns = []
    
    try:
        # Get column information
        cur.execute(f"SELECT * FROM {table_name} LIMIT 0")
        table_columns = [desc[0] for desc in cur.description]
        expected_columns = len(table_columns)
        
        # Get column types
        cur.execute(f"""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = '{table_name}'
        """)
        for col, dtype in cur.fetchall():
            column_info[col] = dtype
            if dtype == 'date':
                idx = table_columns.index(col)
                date_columns.append(idx)
    except Exception as e:
        print(f"Error getting table info: {e}")
        cur.close()
        conn.close()
        return
    finally:
        cur.close()
        conn.close()
    
    # Create a temporary table for staging the data
    staging_table = f"{table_name}_staging"
    conn = connect_db()
    cur = conn.cursor()
    
    try:
        # Create a staging table with all columns as text
        print(f"Creating staging table {staging_table}...")
        cur.execute(f"DROP TABLE IF EXISTS {staging_table}")
        create_stmt = f"CREATE TABLE {staging_table} ("
        for col in table_columns:
            create_stmt += f"{col} TEXT,"
        create_stmt = create_stmt[:-1] + ")"  # Remove trailing comma
        cur.execute(create_stmt)
        conn.commit()
        print(f"Created staging table {staging_table}")
        
        # Load the data into the staging table (all as text)
        row_count = 0
        try:
            with open(csv_path, "r", encoding="utf-8") as f:
                print(f"Copying data to staging table with all columns as TEXT...")
                cur.copy_expert(f"COPY {staging_table} FROM STDIN WITH DELIMITER ',' NULL '' CSV QUOTE '\"'", f)
                conn.commit()
            
            # Verify data was loaded into staging
            cur.execute(f"SELECT COUNT(*) FROM {staging_table}")
            row_count = cur.fetchone()[0]
            print(f"Loaded {row_count} rows into staging table")
            
            if row_count == 0:
                print("WARNING: No data was loaded into staging table!")
                # Try to read a few lines from the CSV to debug
                with open(csv_path, 'r', encoding='utf-8') as f:
                    sample_lines = [next(f) for _ in range(5) if _ < 5]
                    print(f"Sample CSV lines:\n{''.join(sample_lines)}")
                return False
                
        except Exception as e:
            conn.rollback()
            print(f"Error loading data into staging table: {e}")
            return False
        
        # Find and fix problematic date formats directly in the database
        print("Fixing date formats in staging table...")
        for col_idx in date_columns:
            col_name = table_columns[col_idx]
            print(f"Processing date column: {col_name}")
            
            # First check for problematic values
            cur.execute(f"""
                SELECT {col_name}, COUNT(*) 
                FROM {staging_table} 
                WHERE {col_name} ~ '^\d{{4}}[./-]\d{{1,2}}$' 
                GROUP BY {col_name}
                LIMIT 10
            """)
            problem_dates = cur.fetchall()
            if problem_dates:
                print(f"Found problematic date formats in {col_name}: {problem_dates}")
            
            # Handle bad date patterns
            cur.execute(f"""
                UPDATE {staging_table} 
                SET {col_name} = NULL
                WHERE {col_name} IN ('', '--', '-01', '--01') OR {col_name} IS NULL OR TRIM({col_name}) = ''
            """)
            
            # Update year.month format to ISO date
            cur.execute(f"""
                UPDATE {staging_table} 
                SET {col_name} = 
                    regexp_replace({col_name}, '^(\d{{4}})\.(\d{{1,2}})$', '\1-\2-01')
                WHERE {col_name} ~ '^\d{{4}}[.]\d{{1,2}}$'
            """)
            
            # Update year-month format to full ISO date
            cur.execute(f"""
                UPDATE {staging_table} 
                SET {col_name} = 
                    regexp_replace({col_name}, '^(\d{{4}})-(\d{{1,2}})$', '\1-\2-01')
                WHERE {col_name} ~ '^\d{{4}}-\d{{1,2}}$'
            """)
            
            # Update year-only format
            cur.execute(f"""
                UPDATE {staging_table} 
                SET {col_name} = {col_name} || '-01-01'
                WHERE {col_name} ~ '^\d{{4}}$'
            """)
        
        # Insert data from staging to final table
        print(f"Inserting data from {staging_table} to {table_name}...")
        
        # First truncate the target table if needed (uncomment if you want to clear before loading)
        # cur.execute(f"TRUNCATE TABLE {table_name}")
        # conn.commit()
        # print(f"Truncated target table {table_name}")
        
        insert_stmt = f"INSERT INTO {table_name} SELECT "
        for i, col in enumerate(table_columns):
            if column_info[col] == 'date':
                insert_stmt += f"CASE WHEN {col} IS NULL OR {col} = '' THEN NULL ELSE {col}::date END,"
            else:
                insert_stmt += f"{col},"
        insert_stmt = insert_stmt[:-1] + f" FROM {staging_table}"
        
        # Execute the insert with exception handling for any data type issues
        try:
            cur.execute(insert_stmt)
            rows_affected = cur.rowcount
            conn.commit()
            print(f"Data loaded successfully: {rows_affected} records imported into {table_name}.")
            
            # Verify final table has data
            cur.execute(f"SELECT COUNT(*) FROM {table_name}")
            final_count = cur.fetchone()[0]
            print(f"Final table {table_name} now has {final_count} rows")
            
            return rows_affected > 0
        except psycopg2.Error as e:
            conn.rollback()
            print(f"Error during insert: {e}")
            # Try inserting row by row to find problematic records
            print("Attempting row-by-row insert to identify problematic records...")
            
            try:
                # Re-establish the cursor since we rolled back
                cur = conn.cursor()
                cur.execute(f"SELECT * FROM {staging_table}")
                rows = cur.fetchall()
                
                success_count = 0
                error_count = 0
                for i, row in enumerate(rows):
                    try:
                        values = []
                        for j, val in enumerate(row):
                            if column_info[table_columns[j]] == 'date' and val:
                                # Validate and clean date values
                                val = validate_date_string(val)
                                if val:
                                    if re.match(r'^\d{4}\.\d{1,2}$', val):
                                        year, month = val.split('.')
                                        val = f"{year}-{month.zfill(2)}-01"
                                    elif re.match(r'^\d{4}$', val):
                                        val = f"{val}-01-01"
                            values.append(val)
                        
                        placeholders = ','.join(['%s'] * len(values))
                        cur.execute(f"INSERT INTO {table_name} VALUES ({placeholders})", values)
                        conn.commit()
                        success_count += 1
                        
                        # Print progress every 1000 rows
                        if success_count % 1000 == 0:
                            print(f"Processed {success_count} rows...")
                    except Exception as e:
                        conn.rollback()
                        error_count += 1
                        if error_count < 10:  # Only show first 10 errors
                            print(f"Error at row {i+1}: {e}")
                            print(f"Problematic data: {row}")
                
                print(f"Row-by-row insert completed: {success_count} of {len(rows)} rows imported")
                print(f"Skipped {error_count} problematic rows")
            except Exception as e:
                print(f"Error during row-by-row processing: {e}")
            return False
    except Exception as e:
        conn.rollback()
        print(f"Unexpected error: {e}")
        return False
    finally:
        # Clean up staging table
        try:
            cur.execute(f"DROP TABLE IF EXISTS {staging_table}")
            conn.commit()
            print(f"Dropped staging table {staging_table}")
        except Exception as e:
            print(f"Error dropping staging table: {e}")
        cur.close()
        conn.close()

# Add a function to check and fix Excel files if needed
def fix_excel_file(file_path):
    """Attempt to fix problematic Excel files by converting them to a known format"""
    try:
        import subprocess
        
        # Method 1: Try with LibreOffice if available
        try:
            result = subprocess.run(['which', 'libreoffice'], capture_output=True, text=True)
            if result.returncode == 0:
                temp_dir = os.path.dirname(file_path)
                basename = os.path.basename(file_path)
                name_without_ext = os.path.splitext(basename)[0]
                
                print(f"Attempting to fix {basename} using LibreOffice...")
                # First try converting to CSV (more reliable)
                cmd = [
                    'libreoffice', '--headless', '--convert-to', 'csv:Text - txt - csv (StarCalc):44,34,76',
                    '--outdir', temp_dir, file_path
                ]
                subprocess.run(cmd, check=True, capture_output=True, timeout=60)
                
                # Check if conversion worked
                csv_path = os.path.join(temp_dir, f"{name_without_ext}.csv")
                if os.path.exists(csv_path):
                    # Convert CSV back to XLSX
                    # Use on_bad_lines instead of error_bad_lines (for newer pandas versions)
                    df = pd.read_csv(csv_path, encoding='utf-8', on_bad_lines='skip')
                    xlsx_path = os.path.join(temp_dir, f"{name_without_ext}_fixed.xlsx")
                    df.to_excel(xlsx_path, index=False, engine='openpyxl')
                    
                    if os.path.exists(xlsx_path):
                        os.replace(xlsx_path, file_path)
                        os.remove(csv_path)
                        print(f"Fixed {basename} via CSV conversion")
                        return True
        except Exception as e:
            print(f"LibreOffice conversion failed: {e}")
        
        # Method 2: Try text-based detection
        try:
            # Check if it's a text file that can be directly read
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read(4096)  # Read first 4KB
                
            # If it looks like text data, try saving it as CSV then converting back
            if not content.startswith('\x00') and not content.startswith('PK'):
                # Likely text data, write to CSV
                temp_csv = file_path + ".csv"
                with open(temp_csv, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                # Try reading as CSV and writing back as Excel
                # Use on_bad_lines instead of error_bad_lines
                df = pd.read_csv(temp_csv, encoding='utf-8', on_bad_lines='skip', sep=None, engine='python')
                df.to_excel(file_path, index=False)
                os.remove(temp_csv)
                print(f"Fixed {os.path.basename(file_path)} through text conversion")
                return True
        except Exception as e:
            print(f"Text-based fix failed: {e}")
        
        return False
    except Exception as e:
        print(f"Error fixing Excel file: {e}")
        return False

# New function to install missing dependencies
def check_and_install_dependencies():
    """Install required dependencies if missing"""
    try:
        import importlib
        dependencies = {
            'xlrd': 'xlrd>=2.0.1',
            'openpyxl': 'openpyxl',
            'odfpy': 'odfpy'
        }
        
        missing = []
        for module, package in dependencies.items():
            try:
                importlib.import_module(module)
                print(f"✓ {module} is already installed")
            except ImportError:
                print(f"✗ {module} is missing, will install")
                missing.append(package)
        
        if missing:
            import subprocess
            print(f"Installing missing dependencies: {', '.join(missing)}")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing)
            print("Dependencies installed successfully")
            
            # Reload pandas to recognize newly installed modules
            importlib.reload(pd)
            return True
    except Exception as e:
        print(f"Warning: Failed to install dependencies: {e}")
        print("You may need to manually install: xlrd>=2.0.1, openpyxl, odfpy")
    return False

# Improved function to try opening files as CSV if Excel engines fail
def try_as_csv(file_path, expected_columns):
    """Attempt to read a file as CSV with various delimiters"""
    print(f"Attempting to read {os.path.basename(file_path)} as CSV...")
    
    # Create a temporary CSV filename for testing
    temp_csv = file_path + ".temp"
    shutil.copy2(file_path, temp_csv)
    
    try:
        # Try reading with various delimiters
        for delimiter in [',', ';', '\t', '|']:
            try:
                # Use on_bad_lines instead of error_bad_lines
                df = pd.read_csv(temp_csv, dtype=str, delimiter=delimiter, header=None, 
                                encoding='utf-8', on_bad_lines='skip')
                if df.shape[1] >= expected_columns:
                    print(f"Successfully read as CSV with delimiter '{delimiter}'")
                    os.remove(temp_csv)
                    return df
            except Exception as csv_e:
                pass
    except Exception as e:
        print(f"Error with CSV fallback: {e}")
    
    # Clean up temporary file
    if os.path.exists(temp_csv):
        os.remove(temp_csv)
    
    return None

# Enhanced date validation to avoid invalid date strings like "--01"
def validate_date_string(date_str):
    """Validate and fix date strings"""
    if not date_str or pd.isna(date_str):
        return None
        
    # Clean up the string
    date_str = str(date_str).strip()
    
    # Check if it's an empty or invalid pattern
    if date_str in ('', '--', '-01', '--01'):
        return None
        
    # Make sure it has a valid year component
    if not re.match(r'^\d{4}', date_str):
        return None
        
    return date_str

# Run the Process
if __name__ == "__main__":
    import sys
    
    # Check and install missing dependencies first
    check_and_install_dependencies()
    
    # Check if only preprocessing is requested
    if len(sys.argv) > 1 and sys.argv[1] == "--preprocess":
        if os.path.exists(TEMP_CSV):
            preprocess_csv_for_import(TEMP_CSV, TABLE_NAME)
        else:
            print(f"CSV file {TEMP_CSV} does not exist. Run excel_to_csv first.")
        sys.exit(0)
    
    # Check if CSV exists and we should skip Excel processing
    if len(sys.argv) > 1 and sys.argv[1] == "--skip-excel":
        if os.path.exists(TEMP_CSV):
            load_csv_to_postgresql(TEMP_CSV, TABLE_NAME)
        else:
            print(f"CSV file {TEMP_CSV} does not exist. Cannot skip Excel processing.")
        sys.exit(0)
    
    # Add a new command line option to fix Excel files
    if len(sys.argv) > 1 and sys.argv[1] == "--fix-excel":
        print("Checking for corrupted Excel files...")
        for file in os.listdir(DATA_FOLDER):
            if file.endswith(".xlsx") or file.endswith(".xls"):
                file_path = os.path.join(DATA_FOLDER, file)
                try:
                    # Try to open the file to see if it's corrupted
                    pd.read_excel(file_path, nrows=1)
                    print(f"✓ {file} is valid")
                except Exception as e:
                    print(f"✗ {file} is corrupted: {e}")
                    fix_excel_file(file_path)
        sys.exit(0)
    
    # Standard processing with better error handling
    success = excel_to_csv(DATA_FOLDER, TEMP_CSV, LOADED_FOLDER)
    
    if success:
        # Check if CSV has content before proceeding
        if os.path.exists(TEMP_CSV) and os.path.getsize(TEMP_CSV) > 0:
            success = load_csv_to_postgresql(TEMP_CSV, TABLE_NAME)
        else:
            print("CSV file is missing or empty, skipping database import")
            success = False
            
    # Exit with appropriate code
    sys.exit(0 if success else 1)
