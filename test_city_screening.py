#!/usr/bin/env python3
"""
测试单个城市的政策筛选程序
用于测试Gemini API的政策筛选功能
"""

import os
import sys
import json
import time
import logging
import pandas as pd
import psycopg2
import google.generativeai as genai
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 加载配置
CONFIG_FILE = "policy_config.json"

try:
    with open(CONFIG_FILE, "r") as f:
        config = json.load(f)
    
    DB_CONFIG = config["database"]
    AI_CONFIG = config["ai"]
    GEMINI_API_KEYS = AI_CONFIG["gemini_api_keys"]
    AI_MODEL = AI_CONFIG["model"]
    
    logger.info("✅ Configuration loaded successfully")
except Exception as e:
    logger.error(f"❌ Error loading configuration: {e}")
    sys.exit(1)

# 配置Gemini API
def configure_gemini():
    """配置Gemini API"""
    try:
        api_key = GEMINI_API_KEYS[0]  # 使用第一个API密钥
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel(AI_MODEL)
        logger.info("✅ Gemini API configured successfully")
        return model
    except Exception as e:
        logger.error(f"❌ Error configuring Gemini API: {e}")
        return None

model = configure_gemini()
if not model:
    logger.error("❌ Failed to initialize Gemini model")
    sys.exit(1)

def connect_database():
    """连接PostgreSQL数据库"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        logger.info("✅ Database connection established")
        return conn
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return None

def get_city_policies(conn, city, limit=10):
    """获取指定城市的政策数据（限制数量用于测试），按标题排序"""
    try:
        query = """
        SELECT policy_id, policy_title, policy_level, announce_date,
               issuing_department, city, policy_type
        FROM raw_processed_policies
        WHERE city = %s
        AND (should_be_deleted IS NULL OR should_be_deleted = FALSE)
        ORDER BY policy_title NULLS LAST, policy_id
        LIMIT %s
        """
        df = pd.read_sql(query, conn, params=(city, limit))
        logger.info(f"📊 Found {len(df)} policies for {city} (sorted by title)")
        return df
    except Exception as e:
        logger.error(f"❌ Failed to get policies for {city}: {e}")
        return pd.DataFrame()

def create_prompt(city, policies_df):
    """创建发送给Gemini的提示词"""
    prompt = f"""你是一名中国政策分析专家。请根据下述规则，对输入的每条政策数据，输出一行：policy_id,true/false,具体理由。 

规则如下：
1. 如果政策标题、内容或发布机构只针对{city}以外的城市/区域（如"深圳市"、"东莞市"、"京津冀"等），且与{city}无关，输出true，并注明"仅适用于XX市/地区，与{city}无关"；
2. 如多条记录为同一政策的不同版本、目录、文号、正文片段，仅保留最权威、最正式、最完整一条，其余均输出true，理由为"同一政策仅保留最权威、最正式版本，其余为重复"；
3. 只要政策适用于{city}或与{city}直接相关，输出false，理由为"保留"；
4. 如政策仅针对{city}没有的行业/试点/特殊区域，且本地无实际落地，输出true，理由写"仅针对XX行业/区域，{city}无相关落地"；
5. 不能机械比对，必须结合内容、政策文本、实际落地、政策部门和中国行政常识作专业判断。

每行只输出一条结果，按"policy_id,true/false,理由"格式，不输出任何多余内容或解释。
示例：
116671,true,仅适用于东莞市，与{city}无关
116467,true,同一政策仅保留最权威、最正式版本，其余为重复
116857,false,保留

以下是需要分析的政策数据：

"""
    
    for _, row in policies_df.iterrows():
        policy_info = f"policy_id: {row['policy_id']}, "
        policy_info += f"标题: {row['policy_title'] or '无标题'}, "
        policy_info += f"发布机构: {row['issuing_department'] or '未知'}, "
        policy_info += f"政策层级: {row['policy_level'] or '未知'}, "
        policy_info += f"发布日期: {row['announce_date'] or '未知'}, "
        policy_info += f"政策类型: {row['policy_type'] or '未知'}"
        prompt += policy_info + "\n"
    
    return prompt

def call_gemini_api(prompt):
    """调用Gemini API"""
    try:
        response = model.generate_content(prompt)
        if response.text:
            return response.text.strip()
        else:
            logger.warning("⚠️ Empty response from Gemini API")
            return None
    except Exception as e:
        logger.error(f"❌ Gemini API call failed: {e}")
        return None

def parse_ai_response(response_text):
    """解析AI响应"""
    results = []
    if not response_text:
        return results
    
    logger.info("📝 AI Response:")
    logger.info(response_text)
    logger.info("=" * 50)
    
    lines = [line.strip() for line in response_text.strip().split('\n')
             if line.strip() and 'policy_id' not in line.lower() and not line.startswith('示例')]
    
    for line in lines:
        try:
            if ',' in line:
                parts = line.split(',', 2)
                if len(parts) >= 3:
                    policy_id = int(parts[0].strip())
                    should_delete = parts[1].strip().lower() == 'true'
                    reason = parts[2].strip()
                    results.append((policy_id, should_delete, reason))
                    logger.info(f"✅ Parsed: {policy_id} -> {should_delete} ({reason})")
        except (ValueError, IndexError) as e:
            logger.warning(f"⚠️ Failed to parse line: {line} - {e}")
            continue
    
    return results

def test_city_screening(city_name, limit=5):
    """测试单个城市的政策筛选"""
    logger.info(f"🧪 Testing policy screening for: {city_name}")
    
    # 连接数据库
    conn = connect_database()
    if not conn:
        return
    
    try:
        # 获取城市政策数据
        policies_df = get_city_policies(conn, city_name, limit)
        if policies_df.empty:
            logger.warning(f"⚠️ No policies found for {city_name}")
            return
        
        logger.info(f"📋 Policies to analyze:")
        for _, row in policies_df.iterrows():
            logger.info(f"  {row['policy_id']}: {row['policy_title'][:50]}...")
        
        # 创建提示词
        prompt = create_prompt(city_name, policies_df)
        logger.info(f"📤 Sending prompt to Gemini API...")
        
        # 调用Gemini API
        response = call_gemini_api(prompt)
        if not response:
            logger.error(f"❌ Failed to get response for {city_name}")
            return
        
        # 解析响应
        results = parse_ai_response(response)
        if not results:
            logger.warning(f"⚠️ No valid results parsed for {city_name}")
            return
        
        # 显示结果
        logger.info(f"📊 Analysis Results for {city_name}:")
        for policy_id, should_delete, reason in results:
            action = "DELETE" if should_delete else "KEEP"
            logger.info(f"  Policy {policy_id}: {action} - {reason}")
        
        # 直接更新数据库
        if update_database(conn, results):
            logger.info("✅ Results saved to database successfully")
        else:
            logger.error("❌ Failed to save results to database")
        
    except Exception as e:
        logger.error(f"❌ Error during testing: {e}")
    finally:
        conn.close()
        logger.info("🔚 Database connection closed")

def update_database(conn, results):
    """更新数据库"""
    try:
        with conn.cursor() as cur:
            for policy_id, should_delete, reason in results:
                cur.execute("""
                    UPDATE raw_processed_policies
                    SET should_be_deleted = %s, delete_reason = %s
                    WHERE policy_id = %s
                """, (should_delete, reason, policy_id))
            conn.commit()
        logger.info(f"✅ Updated {len(results)} records in database")
        return True
    except Exception as e:
        logger.error(f"❌ Database update failed: {e}")
        conn.rollback()
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("Usage: python test_city_screening.py <city_name> [limit]")
        print("Example: python test_city_screening.py 惠州市 5")
        sys.exit(1)
    
    city_name = sys.argv[1]
    limit = int(sys.argv[2]) if len(sys.argv) > 2 else 5
    
    test_city_screening(city_name, limit)

if __name__ == "__main__":
    main()
