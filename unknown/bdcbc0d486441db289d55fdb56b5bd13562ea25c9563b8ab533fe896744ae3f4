#!/usr/bin/env python3
"""
Test suite for policy_dimension_grader.py robustness improvements.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add the current directory to the path so we can import the module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the module under test
import policy_dimension_grader as pdg


class TestPolicyDimensionGrader(unittest.TestCase):
    """Test cases for policy dimension grader robustness"""

    def setUp(self):
        """Set up test fixtures"""
        self.sample_policies = [
            (1, "Test Policy 1", "This is a test policy about carbon reduction with specific targets."),
            (2, "Test Policy 2", "Another policy about environmental protection and sustainability.")
        ]
        
        self.sample_dimension_results = [
            {
                'policy_id': 0,
                'target_clarity': 2,
                'legal_binding': 1,
                'financial_support': 1,
                'implementation_mechanism': 2,
                'duration': 2,
                'supporting_policies': 1
            },
            {
                'policy_id': 1,
                'target_clarity': 1,
                'legal_binding': 2,
                'financial_support': 0,
                'implementation_mechanism': 1,
                'duration': 1,
                'supporting_policies': 2
            }
        ]

    def test_validate_dimension_scores_valid(self):
        """Test validation of valid dimension scores"""
        valid_scores = {
            'target_clarity': 2,
            'legal_binding': 3,
            'financial_support': 1,
            'implementation_mechanism': 2,
            'duration': 0,
            'supporting_policies': 2
        }
        self.assertTrue(pdg.validate_dimension_scores(valid_scores))

    def test_validate_dimension_scores_invalid(self):
        """Test validation of invalid dimension scores"""
        # Test out of range scores
        invalid_scores = {
            'target_clarity': 5,  # Should be 0-2
            'legal_binding': -1,  # Should be 0-3
            'financial_support': 3,  # Should be 0-2
        }
        self.assertFalse(pdg.validate_dimension_scores(invalid_scores))

    def test_validate_dimension_scores_non_integer(self):
        """Test validation with non-integer scores"""
        invalid_scores = {
            'target_clarity': 1.5,  # Should be integer
            'legal_binding': "2",   # Should be integer
        }
        self.assertFalse(pdg.validate_dimension_scores(invalid_scores))

    def test_get_unprocessed_policies_empty_batch_size(self):
        """Test get_unprocessed_policies with invalid batch size"""
        result = pdg.get_unprocessed_policies(0)
        self.assertEqual(result, [])
        
        result = pdg.get_unprocessed_policies(-1)
        self.assertEqual(result, [])

    @patch('policy_dimension_grader.get_db_connection')
    def test_get_unprocessed_policies_db_error(self, mock_db_conn):
        """Test get_unprocessed_policies with database error"""
        # Mock database connection to raise an exception
        mock_db_conn.side_effect = Exception("Database connection failed")
        
        result = pdg.get_unprocessed_policies(5)
        self.assertEqual(result, [])

    def test_analyze_policy_dimensions_empty_input(self):
        """Test analyze_policy_dimensions with empty input"""
        result = pdg.analyze_policy_dimensions([], [])
        self.assertIsNone(result)

    def test_analyze_policy_dimensions_mismatched_input(self):
        """Test analyze_policy_dimensions with mismatched input lengths"""
        policy_texts = ["Text 1", "Text 2"]
        policy_titles = ["Title 1"]  # Mismatched length
        
        result = pdg.analyze_policy_dimensions(policy_texts, policy_titles)
        self.assertIsNone(result)

    def test_update_policy_dimensions_empty_input(self):
        """Test update_policy_dimensions with empty input"""
        result = pdg.update_policy_dimensions([], [])
        self.assertEqual(result, 0)
        
        result = pdg.update_policy_dimensions([1, 2], [])
        self.assertEqual(result, 0)

    def test_process_policy_batch_empty_input(self):
        """Test process_policy_batch with empty input"""
        result = pdg.process_policy_batch([])
        self.assertEqual(result, 0)

    def test_rate_limit_tracking(self):
        """Test rate limit tracking functionality"""
        # Reset tracker
        pdg._rate_limit_tracker['consecutive_errors'] = 0
        pdg._rate_limit_tracker['last_error_time'] = 0
        
        # Initially should not be rate limited
        self.assertFalse(pdg.check_rate_limit_status())
        
        # Record a successful request
        pdg.record_successful_request()
        self.assertEqual(pdg._rate_limit_tracker['consecutive_errors'], 0)
        
        # Record a failed request
        pdg.record_failed_request()
        self.assertEqual(pdg._rate_limit_tracker['total_requests'], 2)

    @patch('policy_dimension_grader.time.sleep')
    def test_handle_rate_limit_error(self, mock_sleep):
        """Test rate limit error handling"""
        error = Exception("429 Rate limit exceeded")
        
        # Reset tracker
        pdg._rate_limit_tracker['consecutive_errors'] = 0
        
        pdg.handle_rate_limit_error(error)
        
        # Should have incremented consecutive errors
        self.assertGreater(pdg._rate_limit_tracker['consecutive_errors'], 0)
        
        # Should have called sleep
        mock_sleep.assert_called()


class TestConfigurationLoading(unittest.TestCase):
    """Test configuration loading robustness"""

    @patch('policy_dimension_grader.os.path.exists')
    def test_load_config_file_not_found(self, mock_exists):
        """Test configuration loading when file doesn't exist"""
        mock_exists.return_value = False
        
        with self.assertRaises(FileNotFoundError):
            pdg.load_config()

    @patch('builtins.open')
    @patch('policy_dimension_grader.os.path.exists')
    def test_load_config_invalid_json(self, mock_exists, mock_open):
        """Test configuration loading with invalid JSON"""
        mock_exists.return_value = True
        mock_open.return_value.__enter__.return_value.read.return_value = "invalid json"
        
        with self.assertRaises(Exception):
            pdg.load_config()


if __name__ == '__main__':
    # Set up logging for tests
    import logging
    logging.basicConfig(level=logging.DEBUG)
    
    unittest.main()
