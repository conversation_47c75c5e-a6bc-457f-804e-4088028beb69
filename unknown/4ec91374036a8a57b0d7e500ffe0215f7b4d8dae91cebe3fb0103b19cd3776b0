# Multi-API Key System Documentation

## Overview

The policy dimension grader now supports multiple Gemini API keys with intelligent automatic rotation. This system dramatically increases processing capacity and provides resilience against quota exhaustion.

## Key Features

### 1. **Multiple API Key Support**
- Configure multiple API keys in `policy_config.json`
- Automatic rotation when daily limits are reached
- Backward compatibility with single key configuration

### 2. **Intelligent Key Management**
- Real-time monitoring of each key's usage
- Automatic detection of quota exhaustion
- Smart rotation to available keys
- Cooldown periods for problematic keys

### 3. **Enhanced Throughput**
- **Before**: 1,500 requests/day (single key)
- **After**: 1,500 × N requests/day (N = number of keys)
- Example: 5 keys = 7,500 requests/day capacity

## Configuration

### New Configuration Format

Update your `policy_config.json`:

```json
{
    "ai": {
        "gemini_api_keys": [
            "AIzaSyBsBxtFFItzEQUWXHlv1hWdJsvx9wqoe_Y",
            "AIzaSyC1234567890abcdefghijklmnopqrstuvw",
            "AIzaSyD9876543210zyxwvutsrqponmlkjihgfed",
            "AIzaSyE1111222233334444555566667777888899",
            "AIzaSyF9999888877776666555544443333222211"
        ],
        "model": "gemini-2.0-flash-lite",
        "max_retries": 5,
        "retry_delay": 2
    }
}
```

### Legacy Support

The system still supports the old single-key format:

```json
{
    "ai": {
        "gemini_api_key": "AIzaSyBsBxtFFItzEQUWXHlv1hWdJsvx9wqoe_Y",
        "model": "gemini-2.0-flash-lite"
    }
}
```

## How It Works

### 1. **Key Rotation Logic**

```
Key 1 (Active) → Processes requests until daily limit (1,500)
    ↓
Key 1 (Exhausted) → Automatically rotate to Key 2
    ↓
Key 2 (Active) → Continues processing
    ↓
... and so on until all keys are used
    ↓
Next day → All keys reset, start with Key 1 again
```

### 2. **Quota Monitoring**

Each API key tracks:
- **Daily requests**: Current usage vs 1,500 limit
- **Success rate**: Successful vs failed requests
- **Error patterns**: Consecutive failures trigger rotation
- **Cooldown status**: Temporary unavailability periods

### 3. **Automatic Recovery**

- **Daily Reset**: All quotas reset at midnight
- **Error Recovery**: Keys marked as problematic get 1-hour cooldown
- **Smart Retry**: Failed requests trigger key rotation when appropriate

## Benefits

### 1. **Massive Throughput Increase**
- **5 keys**: 7,500 requests/day (5× increase)
- **10 keys**: 15,000 requests/day (10× increase)
- **Processing capacity**: ~50,000-150,000 policies/day (depending on batch size)

### 2. **High Availability**
- No single point of failure
- Automatic failover between keys
- Graceful degradation when keys are exhausted

### 3. **Cost Optimization**
- Maximize usage of each key's daily quota
- Intelligent load distribution
- Minimal waste due to unused quotas

### 4. **Monitoring & Visibility**
- Real-time usage statistics per key
- Detailed logging of rotation events
- Performance metrics and success rates

## Usage Statistics

The system provides comprehensive monitoring:

```
=== API Key Usage Summary ===
Key #1: 1500/1500 requests, 1485 successful - EXHAUSTED
Key #2: 847/1500 requests, 839 successful - ACTIVE (CURRENT)
Key #3: 0/1500 requests, 0 successful - ACTIVE
Key #4: 0/1500 requests, 0 successful - ACTIVE
Key #5: 0/1500 requests, 0 successful - ACTIVE
Overall: 2324/2347 requests successful (99.0%)
=============================
```

## Error Handling

### 1. **Quota Exhaustion**
```
2024-01-15 14:30:25 - WARNING - API key #1 appears to have reached daily limit
2024-01-15 14:30:25 - INFO - Rotated to API key #2
```

### 2. **Rate Limiting**
```
2024-01-15 14:30:30 - WARNING - API key #2 hit rate limit (temporary)
2024-01-15 14:30:30 - INFO - Rate limit compliance: waiting 2.0 seconds
```

### 3. **All Keys Exhausted**
```
2024-01-15 23:45:00 - ERROR - All API keys are exhausted or unavailable
2024-01-15 23:45:00 - INFO - Waiting until quota reset...
```

## Best Practices

### 1. **Key Management**
- Use 3-5 keys for small to medium workloads
- Use 5-10 keys for large-scale processing
- Monitor key usage patterns regularly

### 2. **Batch Size Optimization**
- With multiple keys: batch_size can be 10-15
- Monitor rate limits per key
- Adjust based on processing patterns

### 3. **Monitoring**
- Check logs every few hours during large runs
- Monitor success rates per key
- Replace problematic keys promptly

## Troubleshooting

### Issue: Key rotation not working
**Solution**: Check API key validity and network connectivity

### Issue: All keys exhausted quickly
**Solution**: Reduce batch size or add more keys

### Issue: Low success rate on specific key
**Solution**: Replace the problematic key

## Migration Guide

### From Single Key to Multi-Key

1. **Backup current config**:
   ```bash
   cp policy_config.json policy_config.json.backup
   ```

2. **Update configuration**:
   ```json
   "gemini_api_keys": ["old_key", "new_key_2", "new_key_3"]
   ```

3. **Test with small batch**:
   ```bash
   # Set small batch size for testing
   # Monitor logs for successful rotation
   ```

4. **Scale up gradually**:
   ```bash
   # Increase batch size as confidence grows
   # Add more keys as needed
   ```

## Performance Expectations

### Single Key (Before)
- **Max throughput**: 1,500 requests/day
- **Processing capacity**: ~5,000-15,000 policies/day
- **Downtime**: 100% when quota exhausted

### Multi-Key (After)
- **Max throughput**: 1,500 × N requests/day
- **Processing capacity**: ~25,000-75,000 policies/day (5 keys)
- **Downtime**: Near 0% with proper key management

The multi-key system transforms the policy grader from a single-quota limited tool into a high-throughput, enterprise-ready processing system.
