{"database": {"dbname": "mydb", "user": "xiuli", "password": "73Lxf1017", "host": "localhost", "port": 5432}, "paths": {"data_folder": "/home/<USER>/data", "loaded_folder": "/home/<USER>/loaded", "temp_csv": "/home/<USER>/temp_data.csv"}, "table_name": "policy_data", "batch_size": 10, "ai": {"gemini_api_keys": ["AIzaSyBsBxtFFItzEQUWXHlv1hWdJsvx9wqoe_Y", "AIzaSyC1234567890abcdefghijklmnopqrstuvw", "AIzaSyD9876543210zyxwvutsrqponmlkjihgfed", "AIzaSyE1111222233334444555566667777888899", "AIzaSyF9999888877776666555544443333222211"], "model": "gemini-2.0-flash-lite", "max_retries": 5, "retry_delay": 2}}