import os
import time
import pandas as pd
import google.generativeai as genai
import csv
import json

# Load configuration from policy_config.json
CONFIG_FILE = "/opt/app/PolicyDW/policy_config.json"

with open(CONFIG_FILE, "r") as f:
    config = json.load(f)

# AI Configuration
AI_CONFIG = config.get("ai", {})
GEMINI_API_KEY = AI_CONFIG.get("gemini_api_key", "")
AI_MODEL = AI_CONFIG.get("model", "gemini-2.0-flash-lite")
MAX_RETRIES = AI_CONFIG.get("max_retries", 5)
RETRY_DELAY = AI_CONFIG.get("retry_delay", 2)

# Configure Gemini API Key
genai.configure(api_key=GEMINI_API_KEY)

# Set up the Gemini model
model = genai.GenerativeModel(AI_MODEL)

# Directory containing Excel files
DATA_DIRECTORY = "/opt/app/PolicyDW/data"

# Output directory for processed CSV files
OUTPUT_DIRECTORY = "/opt/app/PolicyDW/output"

# Ensure output directory exists
os.makedirs(OUTPUT_DIRECTORY, exist_ok=True)

# Output CSV file path
OUTPUT_CSV = "processed_policies.csv"

# CSV headers (excluding Full_Article, which will be added locally)
CSV_HEADERS = [
    "Policy_Title", "Is_Valid_Policy", "Is_Low_Carbon", "Policy_Level", "Is_related_to_city",
    "Target_Sectors", "Policy_Strength", "Policy_Strength_Justification", "Issuing_Department", "Announcement_Date"
]

# Read all Excel files in the directory
def get_excel_files(directory):
    return [os.path.join(directory, f) for f in os.listdir(directory) if f.endswith(".xlsx")]

# Process each Excel file
def process_excel_file(file_path):
    print(f"📂 Processing: {file_path}")
    df = pd.read_excel(file_path)

    # Generate output filename (same name but in output directory with .csv extension)
    base_filename = os.path.basename(file_path)
    filename_without_ext = os.path.splitext(base_filename)[0]
    output_file = os.path.join(OUTPUT_DIRECTORY, f"{filename_without_ext}.csv")
    
    # Check if Full_Article column exists
    if "Full_Article" not in df.columns:
        print(f"⚠️ Skipping {file_path}, no Full_Article column found")
        return

    records = []  # Store records for batch processing
    article_texts = []  # Store Full_Article locally
    for index, row in df.iterrows():
        full_text = row["Full_Article"]
        policy_title = row["Policy_title"] if "Policy_title" in row else "Unknown Policy"

        records.append({
            "Policy_Title": policy_title,
            "Full_Article": full_text  # This is what we send to Gemini
        })
        article_texts.append(full_text)

        # Process in batches of 10
        if len(records) == 10:
            process_batch(records, article_texts, output_file)
            records, article_texts = [], []

    # Process the last batch (if it has less than 10 records)
    if records:
        process_batch(records, article_texts, output_file)

# Process batch of 10 records using Gemini
def process_batch(records, article_texts, output_file):
    print(f"🔍 Sending {len(records)} records to Gemini for processing...")

    # Create batch prompt (send full policy texts for analysis)
    batch_input = "\n\n".join(
        [f"政策 {i+1}:\n{record['Full_Article']}" for i, record in enumerate(records)]
    )

    ai_response = analyze_policies_with_gemini(batch_input, len(records))

    # Retry logic if Gemini returns fewer than expected
    retry_count = 0
    while ai_response is None or len(ai_response) < len(records):
        retry_count += 1
        print(f"⚠️ Gemini returned {len(ai_response) if ai_response else 0} records instead of {len(records)}. Retrying... ({retry_count}/10)")
        if retry_count >= 10:
            print("❌ Maximum retries reached. Skipping this batch.")
            return
        time.sleep(2)  # Small delay before retrying
        ai_response = analyze_policies_with_gemini(batch_input, len(records))

    # Append Full_Article from local data before saving
    for i in range(len(ai_response)):
        ai_response[i]["Full_Article"] = article_texts[i]

    save_to_csv(ai_response, output_file)

# Enhanced prompt for better accuracy
def analyze_policies_with_gemini(batch_texts, expected_records, city="未指定城市"):
    prompt = f"""
    你是一个专业的中国低碳政策分析专家，擅长对政府政策文本进行客观分析和归类。我会给你 **{expected_records} 条完整的政策文本**，请你分析每条政策，并返回一个 **严格包含 {expected_records} 条记录的 CSV 格式表格**。

    **分析要求：**
    1. **请确保返回完整的 {expected_records} 条记录**，每条政策对应一条分析记录，缺失信息用"未知"填充
    2. **分析必须客观、准确**，基于政策文本内容，不要主观臆断
    3. **对于城市相关性判断：** 请仔细分析政策是否与城市"{city}"相关，包括该政策是否明确针对该城市制定、实施或适用
    4. **对于政策力度评分：** 请基于政策的约束性、资金投入、量化目标等因素综合评定，1分最弱，10分最强

    **返回格式：**
    - CSV 格式，使用 `"|"` 作为分隔符
    - 返回原始 CSV 文本，不要添加任何额外解释或代码块标记
    - 字段顺序和名称必须精确匹配下方列出的字段，不要改变顺序或名称

    **分析输出字段：**
    1. Policy_Title (政策标题): 从文本中提取的政策正式名称
    2. Is_Valid_Policy (是否有效政策): True/False - 判断是否为正式政策文件而非新闻报道或其他非政策内容
    3. Is_Low_Carbon (是否低碳政策): True/False - 判断政策是否与低碳、减排、节能环保、气候变化相关
    4. Policy_Level (政策级别): 国家级/省级/市级/县级 - 根据发文机构判断
    5. Is_related_to_city (是否与城市相关): True/False - 判断政策是否与当前城市"{city}"直接相关
    6. Target_Sectors (目标行业): 电力、交通、建筑、工业、农业等，用逗号分隔多个行业
    7. Policy_Strength (政策力度): 1-10的整数评分，评估政策约束力和影响力
    8. Policy_Strength_Justification (力度评分理由): 简要说明评分理由，包括强制性措施、资金支持、具体目标等
    9. Issuing_Department (发布部门): 政策发布的具体政府部门或机构
    10. Announcement_Date (发布日期): YYYY-MM-DD格式，如找不到精确日期，可只提供年份如"2020-01-01"

    **政策全文：**
    -------------------------
    {batch_texts}
    -------------------------
    """

    try:
        response = model.generate_content(prompt)
        csv_output = response.text.strip()

        # ✅ Remove Markdown Code Blocks (if they exist)
        if csv_output.startswith("```csv"):
            csv_output = csv_output.replace("```csv", "").replace("```", "").strip()

        # ✅ Ensure the response is a valid CSV format
        parsed_data = parse_csv_response(csv_output, expected_records)

        return parsed_data if len(parsed_data) == expected_records else None

    except Exception as e:
        print(f"❌ Gemini API Error: {e}")
        return None

# Convert AI response CSV string into a list of dicts
def parse_csv_response(csv_text, expected_records):
    lines = csv_text.strip().split("\n")
    
    # ✅ Validate Header
    reader = csv.DictReader(lines, delimiter="|")  # Use "|" as delimiter
    parsed_rows = [row for row in reader]

    # ✅ Ensure expected number of records exist
    if len(parsed_rows) < expected_records:
        print(f"⚠️ Warning: Gemini returned only {len(parsed_rows)} records instead of {expected_records}.")
        return None  # Force retry in process_batch()

    return parsed_rows

# Save processed records to CSV
def save_to_csv(records, output_file):
    file_exists = os.path.exists(output_file)

    with open(output_file, mode="a", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=CSV_HEADERS + ["Full_Article"], delimiter="|")
        
        # Write headers if file is new
        if not file_exists:
            writer.writeheader()
        
        writer.writerows(records)

    print(f"✅ {len(records)} records saved to {output_file}")

# Run the processing
if __name__ == "__main__":
    excel_files = get_excel_files(DATA_DIRECTORY)

    for file in excel_files:
        process_excel_file(file)

    print("🎉 All Excel files processed and saved to CSV!")