#!/bin/bash

# <PERSON><PERSON>t to set up the Gemini API key as an environment variable

# Check if API key is provided as argument
if [ $# -eq 0 ]; then
    read -p "Enter your Gemini API key: " API_KEY
else
    API_KEY=$1
fi

# Save to .env file for persistence
echo "GOOGLE_API_KEY=$API_KEY" > /opt/app/PolicyDW/.env
echo "export GOOGLE_API_KEY=$API_KEY" >> ~/.bashrc

# Set for current session
export GOOGLE_API_KEY=$API_KEY

echo "API key has been set as environment variable GOOGLE_API_KEY"
echo "✅ You can now run the db_ai_processor.py script"
echo ""
echo "To use the API key in future terminal sessions, either:"
echo "  1. Run 'source ~/.bashrc' in a new terminal"
echo "  2. Run this setup script again"
echo "  3. Or use the API key from policy_config.json"
