import pandas as pd
import psycopg2
import sqlalchemy
from sqlalchemy import create_engine
from datetime import datetime as dt

sector_dimension_data = []
sector_id_counter = 1

sector_hierarchy_data = {
    "交通部门": {
    "description": "与交通运输相关的政策，包括客运和货运",
    "child_sectors": {
        "道路运输": {
            "description": "公路客运、货运、出租车等",
            "child_sectors": {
                "公路客运": {"description": "长途客运、城市公交"},
                "公路货运": {"description": "卡车运输、零担物流"},
                "出租车行业": {"description": "传统出租车、网约车"},
                "公路基础设施": {"description": "高速公路、国道、省道建设"}
            }
        },
        "铁路运输": {
            "description": "铁路客运、货运、铁路建设等",
            "child_sectors": {
                "铁路客运": {"description": "高铁、城际铁路、普通铁路客运"},
                "铁路货运": {"description": "集装箱运输、冷链物流"},
                "铁路基础设施": {"description": "轨道铺设、车站建设"}
            }
        },
        "水路运输": {
            "description": "内河航运、海洋运输、港口建设等",
            "child_sectors": {
                "内河运输": {"description": "长江航运、珠江航运"},
                "海洋运输": {"description": "远洋运输、沿海运输"},
                "港口管理": {"description": "港口运营、码头建设"}
            }
        },
        "航空运输": {
            "description": "民航客运、货运、机场建设等",
            "child_sectors": {
                "民航客运": {"description": "国内航线、国际航线"},
                "航空货运": {"description": "货机运输、航空快递"},
                "机场基础设施": {"description": "机场建设、空管系统"}
            }
        },
        "城市公共交通": {
            "description": "公交车、地铁、轻轨、有轨电车等城市公共交通系统",
            "child_sectors": {
                "公交系统": {"description": "公共汽车、BRT快速公交"},
                "轨道交通": {"description": "地铁、轻轨、有轨电车"},
                "共享交通": {"description": "共享单车、共享电动车"}
            }
        },
        "智能交通": {
            "description": "交通信息化、智能化管理系统",
            "child_sectors": {
                "智能交通管理": {"description": "红绿灯智能控制、智能监控"},
                "车联网技术": {"description": "V2X通信、自动驾驶基础设施"},
                "智慧停车": {"description": "智能停车系统、无人收费停车场"}
            }
        },
        "新能源汽车产业": {
            "description": "电动汽车、混合动力汽车等新能源汽车制造、充电基础设施等",
            "child_sectors": {
                "电动汽车制造": {"description": "纯电动乘用车、商用车"},
                "氢燃料汽车": {"description": "氢燃料电池汽车"},
                "充电基础设施": {"description": "充电站、换电站"}
            }
        },
        "共享出行": {
            "description": "共享单车、共享汽车、网约车等",
            "child_sectors": {
                "共享单车": {"description": "公共自行车、智能共享单车"},
                "共享汽车": {"description": "短租电动车、分时租赁"},
                "网约车": {"description": "打车软件、平台经济"}
            }
        },
        "物流业": {
            "description": "仓储、配送、货运代理、供应链管理等",
            "child_sectors": {
                "仓储业": {"description": "冷链仓储、自动化仓库"},
                "运输代理业": {"description": "多式联运和运输代理"},
                "装卸搬运和运输包装": {"description": "装卸搬运、运输货物打包服务"},
                "快递业": {"description": "国内快递、国际快递"}
            }
        },
        "邮政业": {
            "description": "邮件寄递、快递服务等",
            "child_sectors": {
                "邮政服务": {"description": "信件、包裹邮递"},
                "农村邮政": {"description": "偏远地区邮政网络"}
            }
        }
        }    
    },

"工业部门":{ 
         "description": "涵盖各种工业生产和制造活动相关的政策",
        "child_sectors": {
            "先进制造业": {
                "description": "智能制造、3D打印与增材制造、纳米技术制造等",
                "child_sectors": {
                    "智能制造": {
                        "description": "工业机器人、数字化工厂、人工智能制造",
                        "child_sectors": {
                            "工业机器人": {"description": "智能化工业机器人制造"},
                            "数字化工厂": {"description": "基于智能控制系统的数字化生产"},
                            "人工智能制造": {"description": "AI驱动的制造业创新"}
                        }
                    },
                    "3D打印与增材制造": {
                        "description": "医疗3D打印、金属3D打印",
                        "child_sectors": {
                            "医疗3D打印": {"description": "生物医用3D打印技术"},
                            "金属3D打印": {"description": "金属零件3D打印"}
                        }
                    },
                    "纳米技术制造": {"description": "纳米材料及相关制造技术"}
                }
            },
            "新材料": {
                "description": "高性能材料、半导体材料等",
                "child_sectors": {
                    "高性能材料": {
                        "description": "碳纤维及复合材料、石墨烯材料",
                        "child_sectors": {
                            "碳纤维及复合材料": {"description": "高强度轻质复合材料"},
                            "石墨烯材料": {"description": "超导电新型材料"}
                        }
                    },
                    "半导体材料": {
                        "description": "集成电路制造、先进封装",
                        "child_sectors": {
                            "集成电路制造": {"description": "芯片制造"},
                            "先进封装": {"description": "高端半导体封装技术"}
                        }
                    }
                }
            },
            "生物制造": {
                "description": "合成生物学、基因工程、细胞工厂",
                "child_sectors": {
                    "合成生物学": {"description": "基于DNA合成的生物制造"},
                    "基因工程": {"description": "基因编辑与精准医疗"},
                    "细胞工厂": {"description": "细胞培养及应用"}
                }
            },
            "钢铁行业": {"description": "钢铁冶炼、压延加工等"},
            "水泥行业": {"description": "水泥制造及相关产业"},
            "化工行业": {
                "description": "化学原料及化学制品制造业",
                "child_sectors": {
                    "基础化学原料制造": {"description": "基本有机化学原料、基本无机化学原料等"},
                    "专用化学产品制造": {"description": "农药、涂料、染料、颜料等"},
                    "日用化学产品制造": {"description": "化妆品、洗涤用品、口腔清洁用品等"}
                }
            },
            "装备制造业": {
                "description": "通用设备、专用设备、交通运输设备制造等",
                "child_sectors": {
                    "通用设备制造业": {"description": "通用零部件、通用设备制造"},
                    "专用设备制造业": {"description": "农、林、牧、渔专用机械制造，矿山、冶金、建筑专用设备制造等"},
                    "汽车制造业": {"description": "汽车整车制造、汽车零部件及配件制造"},
                    "铁路、船舶、航空航天和其他运输设备制造业": {"description": "铁路机车车辆制造，船舶及相关装置制造，航空、航天器及设备制造等"}
                }
            },
            "轻工业": {
                "description": "食品、饮料、纺织、服装、造纸等轻工制造",
                "child_sectors": {
                    "食品制造业": {"description": "农副食品加工、食品制造"},
                    "饮料制造业": {"description": "酒、饮料及精制茶制造业"},
                    "纺织业": {"description": "纺织、纺织服装、服饰业"},
                    "造纸和纸制品业": {"description": "造纸及纸制品业"},
                    "皮革、毛皮、羽毛及其制品和制鞋业": {"description": "皮革、毛皮、羽毛及其制品和制鞋业"}
                }
            }
        }
    },

"能源部门": {
        "description": "与能源生产、消费、转型相关的政策",
        "child_sectors": {
            "电力行业": {
                "description": "发电、输电、配电、电力市场等",
                "child_sectors": {
                    "火电": {"description": "燃煤发电、燃气发电"},
                    "水电": {"description": "水力发电"},
                    "核电": {"description": "核能发电"},
                    "风电": {"description": "风力发电"},
                    "太阳能发电": {"description": "光伏发电、光热发电"},
                    "生物质能发电": {"description": "生物质发电"},
                    "地热能发电": {"description": "地热发电"},
                    "电网": {"description": "特高压、智能电网、配电网"},
                    "电力市场": {"description": "电力交易、辅助服务市场"}
                }
            },
            "煤炭行业": {"description": "煤炭开采、洗选、加工等"},
            "石油天然气行业": {
                "description": "石油天然气勘探、开采、炼制、储运等",
                "child_sectors": {
                    "石油开采": {"description": "原油勘探与开采"},
                    "天然气开采": {"description": "天然气开采及液化"},
                    "炼油": {"description": "石油加工与精炼"},
                    "油气储运": {"description": "油气管道运输及储存"}
                }
            },
            "可再生能源行业": {
                "description": "风能、太阳能、水能、生物质能、地热能等可再生能源开发利用",
                "child_sectors": {
                    "风能": {"description": "风力发电及风电设备制造"},
                    "太阳能": {"description": "光伏发电、太阳能热发电"},
                    "水能": {"description": "水力发电站建设与运营"},
                    "生物质能": {"description": "生物质发电及燃料利用"},
                    "地热能": {"description": "地热资源开发与利用"}
                }
            },
            "新型储能产业": {
                "description": "电化学储能、物理储能等新型储能技术和产业",
                "child_sectors": {
                    "电池储能": {"description": "锂电池、钠电池、固态电池"},
                    "机械储能": {"description": "抽水蓄能、飞轮储能"},
                    "氢能储能": {"description": "氢气制备、存储、燃料电池"}
                }
            },
            "氢能产业": {
                "description": "氢气制备、储存、运输、加氢站、氢燃料电池等氢能产业链",
                "child_sectors": {
                    "氢气制备": {"description": "电解水制氢、化学法制氢"},
                    "氢气存储": {"description": "高压储氢、液态储氢"},
                    "加氢站": {"description": "加氢基础设施建设"},
                    "氢燃料电池": {"description": "燃料电池及应用"}
                }
            },
            "能源化工": {"description": "煤化工、石油化工、天然气化工等能源与化工结合的产业"},
            "节能服务业": {"description": "提供节能咨询、诊断、改造、评估等服务的产业"},
            "能源互联网": {"description": "能源与互联网技术融合的新型能源系统"},
            "智慧能源": {"description": "利用信息技术提升能源系统智能化水平"},
            "分布式能源": {"description": "在用户侧分散式布局的能源供应系统"}
        }
    },

"建筑部门": {
        "description": "与城乡建设、建筑节能相关的政策",
        "child_sectors": {
            "城乡建设": {
                "description": "城市规划、市政建设、基础设施建设等",
                "child_sectors": {
                    "城市规划": {"description": "城市发展规划、土地利用规划"},
                    "市政建设": {"description": "道路、桥梁、排水系统等"},
                    "基础设施建设": {"description": "供水、燃气、电信等基础设施"}
                }
            },
            "绿色建筑": {
                "description": "绿色建筑设计、建造、评估认证等",
                "child_sectors": {
                    "绿色建筑设计": {"description": "可持续建筑设计"},
                    "绿色施工": {"description": "节能环保建筑施工技术"},
                    "绿色建筑认证": {"description": "LEED、BREEAM、国内绿建标准"}
                }
            },
            "建筑节能": {
                "description": "既有建筑节能改造、新建建筑节能标准提升等",
                "child_sectors": {
                    "既有建筑改造": {"description": "节能改造、绿色化升级"},
                    "新建建筑节能": {"description": "高效能建筑设计标准"}
                }
            },
            "装配式建筑": {
                "description": "工厂化生产、现场装配的建筑方式",
                "child_sectors": {
                    "模块化建筑": {"description": "预制构件、模块化房屋"},
                    "装配式施工": {"description": "现场快速拼装施工"}
                }
            },
            "超低能耗建筑": {
                "description": "极低能耗的建筑设计和技术",
                "child_sectors": {
                    "被动式建筑": {"description": "零能耗建筑、超低能耗住宅"},
                    "智能建筑能源管理": {"description": "智能控制、节能管理系统"}
                }
            },
            "清洁供暖": {
                "description": "清洁能源供暖技术推广和应用",
                "child_sectors": {
                    "地源热泵": {"description": "地热能供暖"},
                    "空气源热泵": {"description": "高效空气能供暖"},
                    "太阳能供暖": {"description": "太阳能与建筑一体化供暖"}
                }
            },
            "建筑材料": {
                "description": "绿色建材、新型建材的研发和应用",
                "child_sectors": {
                    "绿色建材": {"description": "可再生、环保建材"},
                    "高性能建筑材料": {"description": "保温隔热、防火材料"}
                }
            },
            "智能建造": {
                "description": "建筑行业智能化、信息化升级",
                "child_sectors": {
                    "建筑BIM技术": {"description": "建筑信息模型应用"},
                    "建筑机器人": {"description": "自动化施工机器人"},
                    "智能施工管理": {"description": "大数据、物联网在建筑中的应用"}
                }
            },
            "农村建筑节能": {
                "description": "农村房屋节能改造和新建节能农房",
                "child_sectors": {
                    "农村节能改造": {"description": "农村房屋节能升级"},
                    "新型农房建设": {"description": "绿色乡村建筑"}
                }
            }
        }
    },

"其他部门": {
        "description": "不属于以上主要部门，但与低碳相关的政策",
        "child_sectors": {
            "农业": {
                "description": "种植业、畜牧业、渔业等农业领域的低碳发展",
                "child_sectors": {
                    "种植业": {"description": "农作物种植与管理"},
                    "畜牧业": {"description": "畜禽养殖与牧场管理"},
                    "渔业": {"description": "水产养殖与捕捞"}
                }
            },
            "林业": {
                "description": "森林碳汇、生态林业建设等",
                "child_sectors": {
                    "森林保护": {"description": "森林资源管理与保护"},
                    "生态造林": {"description": "植树造林与生态修复"}
                }
            },
            "碳捕集利用与封存 (CCUS)": {
                "description": "二氧化碳捕集、利用和封存技术",
                "child_sectors": {
                    "碳捕集": {"description": "二氧化碳捕获技术"},
                    "碳利用": {"description": "CO2资源化利用"},
                    "碳封存": {"description": "深层地质封存"}
                }
            },
            "循环经济": {
                "description": "资源循环利用、废弃物资源化等",
                "child_sectors": {
                    "废弃物再利用": {"description": "固体废弃物回收与再利用"},
                    "再制造": {"description": "旧设备及材料再制造"}
                }
            },
            "碳交易": {"description": "碳排放权交易市场"},
            "生态环境": {
                "description": "生态环境保护、生态修复、生态文明建设等",
                "child_sectors": {
                    "环境治理": {"description": "污染治理与修复"},
                    "生态修复": {"description": "自然环境恢复与保护"}
                }
            },
            "科技研发": {
                "description": "低碳技术研发、创新体系建设等",
                "child_sectors": {
                    "绿色科技": {"description": "绿色能源及节能技术"},
                    "碳中和技术": {"description": "碳排放削减与碳补偿技术"}
                }
            },
            "金融支持": {
                "description": "绿色金融、碳金融等",
                "child_sectors": {
                    "绿色信贷": {"description": "支持环保项目的金融产品"},
                    "碳基金": {"description": "碳减排投资基金"}
                }
            },
            "政策机制": {
                "description": "碳达峰碳中和政策体系、制度建设等",
                "child_sectors": {
                    "碳中和战略": {"description": "碳达峰目标与政策"},
                    "低碳法规": {"description": "环保法律法规与标准"}
                }
            },
            "碳监测与核算": {
                "description": "碳排放监测、报告、核算体系建设",
                "child_sectors": {
                    "碳足迹监测": {"description": "企业及产品碳排放计算"},
                    "碳数据管理": {"description": "碳排放数据分析与优化"}
                }
            }
        }
    }
}


def create_sector_data(hierarchy, parent_id=None):
    global sector_id_counter
    for sector_name, sector_info in hierarchy.items():
        sector_record = {
            'sector_id': sector_id_counter,
            'sector_name': sector_name.lower(),  # Convert to lowercase
            'parent_sector_id': parent_id,  # Changed to lowercase
            'sector_description': sector_info.get('description'),  # Changed to lowercase
            'created_at': dt.now(), # Changed to lowercase
            'updated_at': dt.now() # Changed to lowercase
        }
        sector_dimension_data.append(sector_record)
        sector_id_counter += 1
        if 'child_sectors' in sector_info:
            create_sector_data(sector_info['child_sectors'], parent_id=sector_record['sector_id'])


for top_sector_name, top_sector_info in sector_hierarchy_data.items():
    sector_dimension_data.append({
        'sector_id': sector_id_counter,
        'sector_name': top_sector_name.lower(),  # Convert to lowercase
        'parent_sector_id': None,  # Changed to lowercase
        'sector_description': top_sector_info['description'], # Changed to lowercase
        'created_at': dt.now(), # Changed to lowercase
        'updated_at': dt.now()# Changed to lowercase
    })
    top_sector_id = sector_id_counter
    sector_id_counter += 1
    if 'child_sectors' in top_sector_info:
        create_sector_data(top_sector_info['child_sectors'], parent_id=top_sector_id)

df_sector_dimension = pd.DataFrame(sector_dimension_data)
df_sector_dimension = df_sector_dimension.astype({'sector_id': 'Int64', 'parent_sector_id': 'Int64'}) # Changed to lowercase

def populate_sector_dimension(db_connection, sector_df):
    try:
        engine = create_engine(f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}")
        print("Loading Sector Dimension Data...")
        sector_df.to_sql('sector_dimension', con=engine, if_exists='append', index=False,
                         dtype={
                             'sector_id': sqlalchemy.Integer,
                             'sector_name': sqlalchemy.String(100),  # Changed to lowercase
                             'parent_sector_id': sqlalchemy.Integer, # Changed to lowercase
                             'sector_description': sqlalchemy.Text, # Changed to lowercase
                             'created_at': sqlalchemy.DateTime, # Changed to lowercase
                             'updated_at': sqlalchemy.DateTime # Changed to lowercase
                         },
                        )
        print("Sector Dimension table populated successfully.")
    except Exception as e:
        print(f"Error populating Sector Dimension table: {e}")

db_config = {
    "host": "j-5181725-job-0",
    "port": "5432",
    "database": "mydb",
    "user": "ucloud",
    "password": "Abcd1234"
}

db_connection = None
try:
    db_connection = psycopg2.connect(**db_config)
    populate_sector_dimension(db_connection, df_sector_dimension)
except psycopg2.Error as db_e:
    print(f"Database connection error: {db_e}")
finally:
    if db_connection:
        db_connection.close()